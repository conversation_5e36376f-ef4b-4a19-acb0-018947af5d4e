{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification.vue", "mtime": 1753949520583}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["qualification.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAiBA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "qualification.vue", "sourceRoot": "src/views/expertReview", "sourcesContent": ["<template>\r\n  <div>\r\n<!--    <BidHeadthree></BidHeadthree>-->\r\n\t  <div class=\"title\">专家评审系统</div>\r\n    <div class=\"info\">\r\n      <div class=\"content\">\r\n        <one v-if=\"node == 'one'\" @send=\"handleData\"></one>\r\n        <two v-if=\"node == 'two'\" @send=\"handleData\" :isLeader=\"isLeader\" :finish=\"finish\"></two>\r\n        <three ref=\"threeComponent\" v-if=\"node == 'three'\" @send=\"handleData\" :finish=\"finish\"></three>\r\n      </div>\r\n    </div>\r\n    <Foot></Foot>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport one from \"./qualification/one\";\r\nimport two from \"./qualification/two\";\r\nimport three from \"./qualification/three\";\r\n\r\nimport { getProject } from \"@/api/tender/project\";\r\nimport { expertInfoById } from \"@/api/expert/review\";\r\nimport { getEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\r\nimport expertReviewWebSocket from \"@/mixins/expertReviewWebSocket\";\r\n\r\nexport default {\r\n  components: { one, two, three },\r\n  mixins: [expertReviewWebSocket],\r\n  name: \"qualification\",\r\n  data() {\r\n    return {\r\n      project: {},\r\n      projectName: \"测试项目111\",\r\n      node: \"one\",\r\n      finish: false,\r\n      leader: {},\r\n      isLeader: false,\r\n    };\r\n  },\r\n  methods: {\r\n    async init() {\r\n      try {\r\n        // 根据项目id查询项目信息\r\n        const projectResponse = await getProject(this.$route.query.projectId);\r\n        if (projectResponse.code === 200) {\r\n          this.project = projectResponse.data;\r\n        } else {\r\n          this.$message.warning(projectResponse.msg);\r\n        }\r\n\r\n        // 获取专家信息\r\n        const expertResponse = await expertInfoById({\r\n          projectId: this.$route.query.projectId,\r\n        });\r\n        if (expertResponse.code === 200) {\r\n          this.leader = expertResponse.data.find(\r\n            (item) => item.expertLeader === 1\r\n          );\r\n          console.log(\"this.leader\", this.leader);\r\n\r\n          if (this.leader && this.leader.zjhm === this.$route.query.zjhm) {\r\n            this.isLeader = true;\r\n          }\r\n        } else {\r\n          this.$message.warning(expertResponse.msg);\r\n        }\r\n\r\n        // 设置 finish 和 node 的逻辑\r\n        this.finish = this.$route.query.finish === \"true\";\r\n        console.log(\"this.finish\", this.finish, \"this.isLeader\", this.isLeader);\r\n\t      \r\n\t      // 判断当前环境\r\n\t      if (process.env.NODE_ENV === \"development\") {\r\n\t\t      this.node = \"one\";\r\n\t\t      return\r\n\t      }\r\n\t\t\t\t\r\n\t\t\t\t\r\n        // 判断是否满足条件\r\n        if (this.finish && this.isLeader) {\r\n          this.node = \"three\";\r\n        } else if (this.finish && !this.isLeader) {\r\n          this.node = \"two\";\r\n        } else {\r\n          this.getEvalExpertStatus();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error during API calls:\", error);\r\n        // this.$message.error(\"An error occurred while fetching data.\");\r\n      }\r\n    },\r\n    // 查询专家评审节点信息\r\n    getEvalExpertStatus() {\r\n      // 查询专家评审节点信息\r\n      getEvalExpertScoreInfo({\r\n        projectEvaluationId: JSON.parse(\r\n          localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n        ).projectEvaluationId,\r\n        expertResultId: JSON.parse(localStorage.getItem(\"expertResultId\")),\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n        ).scoringMethodItemId,\r\n      }).then((expertStatusResponse) => {\r\n        if (expertStatusResponse.code == 200) {\r\n          localStorage.setItem(\r\n            \"evalExpertScoreInfo\",\r\n            JSON.stringify(expertStatusResponse.data)\r\n          );\r\n          if (expertStatusResponse.data.evalState == 0) {\r\n            this.node = \"one\";\r\n          } else if (expertStatusResponse.data.evalState == 1) {\r\n            this.node = \"two\";\r\n          }else if (expertStatusResponse.data.evalState == 2) {\r\n\t          if (this.isLeader) {\r\n\t\t          this.node = \"three\";\r\n\t          } else {\r\n\t\t          this.node = \"two\";\r\n\t          }\r\n          }\r\n        }\r\n      });\r\n    },\r\n    // 跳转到二次报价\r\n    secondOffer() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      this.$router.push({ path: \"/secondOffer\", query: query });\r\n    },\r\n    handleData(data) {\r\n      // 在切换节点之前，主动清除当前组件的定时器\r\n      if (this.node === 'three' && this.$refs.threeComponent) {\r\n        this.$refs.threeComponent.clearTimer();\r\n      }\r\n      this.node = data;\r\n    },\r\n  },\r\n  mounted() {\r\n    this.init();\r\n    // setInterval(()=>{\r\n    // \tthis.init();\r\n    // },5000)\r\n  },\r\n\r\n  // 路由离开前的守卫\r\n  beforeRouteLeave(to, from, next) {\r\n    // 清除当前活跃组件的定时器\r\n    if (this.node === 'three' && this.$refs.threeComponent) {\r\n      this.$refs.threeComponent.clearTimer();\r\n    }\r\n    next();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.info {\r\n  background-color: #f5f5f5;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n.content {\r\n  background-color: #fff;\r\n  width: 90%;\r\n  min-height: 71vh;\r\n  margin: 20px 0;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n  }\r\n}\r\n.little-title {\r\n  color: rgba(80, 80, 80, 1);\r\n  font-size: 14px;\r\n}\r\n.item-button {\r\n  width: 150px;\r\n  height: 40px;\r\n  margin: 20px 28px;\r\n  color: #176adb;\r\n  background-color: #f5f5f5;\r\n  border: 0;\r\n  font-weight: 700;\r\n  &:hover {\r\n    color: #176adb;\r\n  }\r\n}\r\n.item-button-little {\r\n  border: #333 1px solid;\r\n  width: 124px;\r\n  height: 32px;\r\n  background-color: rgba(151, 253, 246, 1);\r\n  color: rgba(0, 0, 0, 1);\r\n  &:hover {\r\n    color: rgba(0, 0, 0, 1);\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.title{\r\n\tbackground-color: #c8c9cc;\r\n\tpadding: 10px 5%;\r\n}\r\n</style>"]}]}