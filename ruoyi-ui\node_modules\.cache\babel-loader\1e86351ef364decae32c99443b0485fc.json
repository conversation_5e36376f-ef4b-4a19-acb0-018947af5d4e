{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification.vue", "mtime": 1753949520583}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\babel.config.js", "mtime": 1750995480034}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_one", "_interopRequireDefault", "require", "_two", "_three", "_project", "_review", "_expertStatus", "_expertReviewWebSocket", "components", "one", "two", "three", "mixins", "expertReviewWebSocket", "name", "data", "project", "projectName", "node", "finish", "leader", "<PERSON><PERSON><PERSON><PERSON>", "methods", "init", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "projectResponse", "expertResponse", "_t", "w", "_context", "n", "p", "getProject", "$route", "query", "projectId", "v", "code", "$message", "warning", "msg", "expertInfoById", "find", "item", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "zjhm", "process", "env", "NODE_ENV", "a", "getEvalExpertStatus", "error", "_this2", "getEvalExpertScoreInfo", "projectEvaluationId", "JSON", "parse", "localStorage", "getItem", "expertResultId", "scoringMethodItemId", "then", "expertStatusResponse", "setItem", "stringify", "evalState", "secondOffer", "$router", "push", "path", "handleData", "$refs", "threeComponent", "clearTimer", "mounted", "beforeRouteLeave", "to", "from", "next"], "sources": ["src/views/expertReview/qualification.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n<!--    <BidHeadthree></BidHeadthree>-->\r\n\t  <div class=\"title\">专家评审系统</div>\r\n    <div class=\"info\">\r\n      <div class=\"content\">\r\n        <one v-if=\"node == 'one'\" @send=\"handleData\"></one>\r\n        <two v-if=\"node == 'two'\" @send=\"handleData\" :isLeader=\"isLeader\" :finish=\"finish\"></two>\r\n        <three ref=\"threeComponent\" v-if=\"node == 'three'\" @send=\"handleData\" :finish=\"finish\"></three>\r\n      </div>\r\n    </div>\r\n    <Foot></Foot>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport one from \"./qualification/one\";\r\nimport two from \"./qualification/two\";\r\nimport three from \"./qualification/three\";\r\n\r\nimport { getProject } from \"@/api/tender/project\";\r\nimport { expertInfoById } from \"@/api/expert/review\";\r\nimport { getEvalExpertScoreInfo } from \"@/api/evaluation/expertStatus\";\r\nimport expertReviewWebSocket from \"@/mixins/expertReviewWebSocket\";\r\n\r\nexport default {\r\n  components: { one, two, three },\r\n  mixins: [expertReviewWebSocket],\r\n  name: \"qualification\",\r\n  data() {\r\n    return {\r\n      project: {},\r\n      projectName: \"测试项目111\",\r\n      node: \"one\",\r\n      finish: false,\r\n      leader: {},\r\n      isLeader: false,\r\n    };\r\n  },\r\n  methods: {\r\n    async init() {\r\n      try {\r\n        // 根据项目id查询项目信息\r\n        const projectResponse = await getProject(this.$route.query.projectId);\r\n        if (projectResponse.code === 200) {\r\n          this.project = projectResponse.data;\r\n        } else {\r\n          this.$message.warning(projectResponse.msg);\r\n        }\r\n\r\n        // 获取专家信息\r\n        const expertResponse = await expertInfoById({\r\n          projectId: this.$route.query.projectId,\r\n        });\r\n        if (expertResponse.code === 200) {\r\n          this.leader = expertResponse.data.find(\r\n            (item) => item.expertLeader === 1\r\n          );\r\n          console.log(\"this.leader\", this.leader);\r\n\r\n          if (this.leader && this.leader.zjhm === this.$route.query.zjhm) {\r\n            this.isLeader = true;\r\n          }\r\n        } else {\r\n          this.$message.warning(expertResponse.msg);\r\n        }\r\n\r\n        // 设置 finish 和 node 的逻辑\r\n        this.finish = this.$route.query.finish === \"true\";\r\n        console.log(\"this.finish\", this.finish, \"this.isLeader\", this.isLeader);\r\n\t      \r\n\t      // 判断当前环境\r\n\t      if (process.env.NODE_ENV === \"development\") {\r\n\t\t      this.node = \"one\";\r\n\t\t      return\r\n\t      }\r\n\t\t\t\t\r\n\t\t\t\t\r\n        // 判断是否满足条件\r\n        if (this.finish && this.isLeader) {\r\n          this.node = \"three\";\r\n        } else if (this.finish && !this.isLeader) {\r\n          this.node = \"two\";\r\n        } else {\r\n          this.getEvalExpertStatus();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error during API calls:\", error);\r\n        // this.$message.error(\"An error occurred while fetching data.\");\r\n      }\r\n    },\r\n    // 查询专家评审节点信息\r\n    getEvalExpertStatus() {\r\n      // 查询专家评审节点信息\r\n      getEvalExpertScoreInfo({\r\n        projectEvaluationId: JSON.parse(\r\n          localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n        ).projectEvaluationId,\r\n        expertResultId: JSON.parse(localStorage.getItem(\"expertResultId\")),\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n        ).scoringMethodItemId,\r\n      }).then((expertStatusResponse) => {\r\n        if (expertStatusResponse.code == 200) {\r\n          localStorage.setItem(\r\n            \"evalExpertScoreInfo\",\r\n            JSON.stringify(expertStatusResponse.data)\r\n          );\r\n          if (expertStatusResponse.data.evalState == 0) {\r\n            this.node = \"one\";\r\n          } else if (expertStatusResponse.data.evalState == 1) {\r\n            this.node = \"two\";\r\n          }else if (expertStatusResponse.data.evalState == 2) {\r\n\t          if (this.isLeader) {\r\n\t\t          this.node = \"three\";\r\n\t          } else {\r\n\t\t          this.node = \"two\";\r\n\t          }\r\n          }\r\n        }\r\n      });\r\n    },\r\n    // 跳转到二次报价\r\n    secondOffer() {\r\n      const query = {\r\n        projectId: this.$route.query.projectId,\r\n        zjhm: this.$route.query.zjhm,\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"tenderOfferScoringMethodItems\")\r\n        ),\r\n      };\r\n      this.$router.push({ path: \"/secondOffer\", query: query });\r\n    },\r\n    handleData(data) {\r\n      // 在切换节点之前，主动清除当前组件的定时器\r\n      if (this.node === 'three' && this.$refs.threeComponent) {\r\n        this.$refs.threeComponent.clearTimer();\r\n      }\r\n      this.node = data;\r\n    },\r\n  },\r\n  mounted() {\r\n    this.init();\r\n    // setInterval(()=>{\r\n    // \tthis.init();\r\n    // },5000)\r\n  },\r\n\r\n  // 路由离开前的守卫\r\n  beforeRouteLeave(to, from, next) {\r\n    // 清除当前活跃组件的定时器\r\n    if (this.node === 'three' && this.$refs.threeComponent) {\r\n      this.$refs.threeComponent.clearTimer();\r\n    }\r\n    next();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.info {\r\n  background-color: #f5f5f5;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n.content {\r\n  background-color: #fff;\r\n  width: 90%;\r\n  min-height: 71vh;\r\n  margin: 20px 0;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n  }\r\n}\r\n.little-title {\r\n  color: rgba(80, 80, 80, 1);\r\n  font-size: 14px;\r\n}\r\n.item-button {\r\n  width: 150px;\r\n  height: 40px;\r\n  margin: 20px 28px;\r\n  color: #176adb;\r\n  background-color: #f5f5f5;\r\n  border: 0;\r\n  font-weight: 700;\r\n  &:hover {\r\n    color: #176adb;\r\n  }\r\n}\r\n.item-button-little {\r\n  border: #333 1px solid;\r\n  width: 124px;\r\n  height: 32px;\r\n  background-color: rgba(151, 253, 246, 1);\r\n  color: rgba(0, 0, 0, 1);\r\n  &:hover {\r\n    color: rgba(0, 0, 0, 1);\r\n  }\r\n}\r\n.factors {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n.title{\r\n\tbackground-color: #c8c9cc;\r\n\tpadding: 10px 5%;\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;AAiBA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,IAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AAEA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AACA,IAAAK,aAAA,GAAAL,OAAA;AACA,IAAAM,sBAAA,GAAAP,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;iCAEA;EACAO,UAAA;IAAAC,GAAA,EAAAA,YAAA;IAAAC,GAAA,EAAAA,YAAA;IAAAC,KAAA,EAAAA;EAAA;EACAC,MAAA,GAAAC,8BAAA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,WAAA;MACAC,IAAA;MACAC,MAAA;MACAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MAAA,IAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,eAAA,EAAAC,cAAA,EAAAC,EAAA;QAAA,WAAAL,aAAA,CAAAD,OAAA,IAAAO,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAGA,IAAAE,mBAAA,EAAAb,KAAA,CAAAc,MAAA,CAAAC,KAAA,CAAAC,SAAA;YAAA;cAAAV,eAAA,GAAAI,QAAA,CAAAO,CAAA;cACA,IAAAX,eAAA,CAAAY,IAAA;gBACAlB,KAAA,CAAAR,OAAA,GAAAc,eAAA,CAAAf,IAAA;cACA;gBACAS,KAAA,CAAAmB,QAAA,CAAAC,OAAA,CAAAd,eAAA,CAAAe,GAAA;cACA;;cAEA;cAAAX,QAAA,CAAAC,CAAA;cAAA,OACA,IAAAW,sBAAA;gBACAN,SAAA,EAAAhB,KAAA,CAAAc,MAAA,CAAAC,KAAA,CAAAC;cACA;YAAA;cAFAT,cAAA,GAAAG,QAAA,CAAAO,CAAA;cAGA,IAAAV,cAAA,CAAAW,IAAA;gBACAlB,KAAA,CAAAJ,MAAA,GAAAW,cAAA,CAAAhB,IAAA,CAAAgC,IAAA,CACA,UAAAC,IAAA;kBAAA,OAAAA,IAAA,CAAAC,YAAA;gBAAA,CACA;gBACAC,OAAA,CAAAC,GAAA,gBAAA3B,KAAA,CAAAJ,MAAA;gBAEA,IAAAI,KAAA,CAAAJ,MAAA,IAAAI,KAAA,CAAAJ,MAAA,CAAAgC,IAAA,KAAA5B,KAAA,CAAAc,MAAA,CAAAC,KAAA,CAAAa,IAAA;kBACA5B,KAAA,CAAAH,QAAA;gBACA;cACA;gBACAG,KAAA,CAAAmB,QAAA,CAAAC,OAAA,CAAAb,cAAA,CAAAc,GAAA;cACA;;cAEA;cACArB,KAAA,CAAAL,MAAA,GAAAK,KAAA,CAAAc,MAAA,CAAAC,KAAA,CAAApB,MAAA;cACA+B,OAAA,CAAAC,GAAA,gBAAA3B,KAAA,CAAAL,MAAA,mBAAAK,KAAA,CAAAH,QAAA;;cAEA;cAAA,MACAgC,OAAA,CAAAC,GAAA,CAAAC,QAAA;gBAAArB,QAAA,CAAAC,CAAA;gBAAA;cAAA;cACAX,KAAA,CAAAN,IAAA;cAAA,OAAAgB,QAAA,CAAAsB,CAAA;YAAA;cAKA;cACA,IAAAhC,KAAA,CAAAL,MAAA,IAAAK,KAAA,CAAAH,QAAA;gBACAG,KAAA,CAAAN,IAAA;cACA,WAAAM,KAAA,CAAAL,MAAA,KAAAK,KAAA,CAAAH,QAAA;gBACAG,KAAA,CAAAN,IAAA;cACA;gBACAM,KAAA,CAAAiC,mBAAA;cACA;cAAAvB,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAO,CAAA;cAEAS,OAAA,CAAAQ,KAAA,4BAAA1B,EAAA;cACA;YAAA;cAAA,OAAAE,QAAA,CAAAsB,CAAA;UAAA;QAAA,GAAA3B,OAAA;MAAA;IAEA;IACA;IACA4B,mBAAA,WAAAA,oBAAA;MAAA,IAAAE,MAAA;MACA;MACA,IAAAC,oCAAA;QACAC,mBAAA,EAAAC,IAAA,CAAAC,KAAA,CACAC,YAAA,CAAAC,OAAA,gCACA,EAAAJ,mBAAA;QACAK,cAAA,EAAAJ,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;QACAE,mBAAA,EAAAL,IAAA,CAAAC,KAAA,CACAC,YAAA,CAAAC,OAAA,gCACA,EAAAE;MACA,GAAAC,IAAA,WAAAC,oBAAA;QACA,IAAAA,oBAAA,CAAA3B,IAAA;UACAsB,YAAA,CAAAM,OAAA,CACA,uBACAR,IAAA,CAAAS,SAAA,CAAAF,oBAAA,CAAAtD,IAAA,CACA;UACA,IAAAsD,oBAAA,CAAAtD,IAAA,CAAAyD,SAAA;YACAb,MAAA,CAAAzC,IAAA;UACA,WAAAmD,oBAAA,CAAAtD,IAAA,CAAAyD,SAAA;YACAb,MAAA,CAAAzC,IAAA;UACA,WAAAmD,oBAAA,CAAAtD,IAAA,CAAAyD,SAAA;YACA,IAAAb,MAAA,CAAAtC,QAAA;cACAsC,MAAA,CAAAzC,IAAA;YACA;cACAyC,MAAA,CAAAzC,IAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAuD,WAAA,WAAAA,YAAA;MACA,IAAAlC,KAAA;QACAC,SAAA,OAAAF,MAAA,CAAAC,KAAA,CAAAC,SAAA;QACAY,IAAA,OAAAd,MAAA,CAAAC,KAAA,CAAAa,IAAA;QACAe,mBAAA,EAAAL,IAAA,CAAAC,KAAA,CACAC,YAAA,CAAAC,OAAA,iCACA;MACA;MACA,KAAAS,OAAA,CAAAC,IAAA;QAAAC,IAAA;QAAArC,KAAA,EAAAA;MAAA;IACA;IACAsC,UAAA,WAAAA,WAAA9D,IAAA;MACA;MACA,SAAAG,IAAA,qBAAA4D,KAAA,CAAAC,cAAA;QACA,KAAAD,KAAA,CAAAC,cAAA,CAAAC,UAAA;MACA;MACA,KAAA9D,IAAA,GAAAH,IAAA;IACA;EACA;EACAkE,OAAA,WAAAA,QAAA;IACA,KAAA1D,IAAA;IACA;IACA;IACA;EACA;EAEA;EACA2D,gBAAA,WAAAA,iBAAAC,EAAA,EAAAC,IAAA,EAAAC,IAAA;IACA;IACA,SAAAnE,IAAA,qBAAA4D,KAAA,CAAAC,cAAA;MACA,KAAAD,KAAA,CAAAC,cAAA,CAAAC,UAAA;IACA;IACAK,IAAA;EACA;AACA", "ignoreList": []}]}