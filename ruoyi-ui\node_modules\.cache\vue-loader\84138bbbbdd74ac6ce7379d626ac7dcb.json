{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\components\\pdfView\\index-improved.vue?vue&type=style&index=0&id=40af4922&lang=scss&scoped=true", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\components\\pdfView\\index-improved.vue", "mtime": 1753955175758}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750996948772}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750996951274}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750996949033}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750996947786}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnBkZi1sb2FkaW5nLWNvbnRhaW5lciB7DQoJcG9zaXRpb246IGZpeGVkOw0KCXRvcDogMDsNCglsZWZ0OiAwOw0KCXdpZHRoOiAxMDAlOw0KCWhlaWdodDogMTAwdmg7DQoJYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpOw0KCWRpc3BsYXk6IGZsZXg7DQoJanVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQoJYWxpZ24taXRlbXM6IGNlbnRlcjsNCgl6LWluZGV4OiAxMDAwOw0KfQ0KDQoubG9hZGluZy1jb250ZW50IHsNCgliYWNrZ3JvdW5kOiB3aGl0ZTsNCglwYWRkaW5nOiA0MHB4Ow0KCWJvcmRlci1yYWRpdXM6IDEycHg7DQoJYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMTUpOw0KCXRleHQtYWxpZ246IGNlbnRlcjsNCgltaW4td2lkdGg6IDQwMHB4Ow0KCW1heC13aWR0aDogNTAwcHg7DQp9DQoNCi5sb2FkaW5nLXRleHQgew0KCWZvbnQtc2l6ZTogMThweDsNCglmb250LXdlaWdodDogNjAwOw0KCWNvbG9yOiAjMzMzOw0KCW1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5sb2FkaW5nLXByb2dyZXNzIHsNCgltYXJnaW46IDIwcHggMDsNCn0NCg0KLmxvYWRpbmctZGV0YWlsIHsNCglmb250LXNpemU6IDE0cHg7DQoJY29sb3I6ICM2NjY7DQoJbWFyZ2luLXRvcDogMTVweDsNCgltaW4taGVpZ2h0OiAyMHB4Ow0KfQ0KDQoucGRmLXBhZ2Ugew0KCXBvc2l0aW9uOiByZWxhdGl2ZTsNCgltYXJnaW4tYm90dG9tOiAyMHB4Ow0KCXRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLmNhbnZhcy1lbmxhcmdlIHsNCgl6LWluZGV4OiA5OTk7DQoJYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjgpOw0KCXBvc2l0aW9uOiBmaXhlZDsNCgl3aWR0aDogMTAwJTsNCgloZWlnaHQ6IDEwMHZoOw0KCXRvcDogMDsNCglsZWZ0OiAwOw0KCWRpc3BsYXk6IGZsZXg7DQoJanVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQoJYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KI2VubGFyZ2Ugew0KCWJhY2tncm91bmQ6IHdoaXRlOw0KCWJvcmRlci1yYWRpdXM6IDhweDsNCglwYWRkaW5nOiAyMHB4Ow0KCWJveC1zaGFkb3c6IDAgNHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjMpOw0KfQ0K"}, {"version": 3, "sources": ["index-improved.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA00BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index-improved.vue", "sourceRoot": "src/components/pdfView", "sourcesContent": ["<!--\r\n  PDF文件查看器组件 - 增强版\r\n  功能：\r\n  1. 支持PDF文件的在线预览\r\n  2. 提供页面导航功能（上一页、下一页、首页）\r\n  3. 支持PDF页面放大查看\r\n  4. 自动计算当前页码\r\n  5. 虚拟滚动和懒加载，防止大文件浏览器卡死\r\n-->\r\n<template>\r\n\t<div>\r\n\t\t<!-- PDF文件加载进度条 -->\r\n\t\t<div v-if=\"isLoading\" class=\"pdf-loading-container\">\r\n\t\t\t<div class=\"loading-content\">\r\n\t\t\t\t<div class=\"loading-text\">正在加载PDF文件...</div>\r\n\t\t\t\t<el-progress\r\n\t\t\t\t\t:percentage=\"loadingProgress\"\r\n\t\t\t\t\t:stroke-width=\"8\"\r\n\t\t\t\t\t:show-text=\"true\"\r\n\t\t\t\t\t:format=\"formatProgress\"\r\n\t\t\t\t\tstatus=\"success\"\r\n\t\t\t\t\tclass=\"loading-progress\">\r\n\t\t\t\t</el-progress>\r\n\t\t\t\t<div class=\"loading-detail\">{{ loadingDetail }}</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t\r\n\t\t<!-- PDF主容器：固定高度600px，支持滚动查看多页PDF -->\r\n\t\t<div ref=\"pdfContainer\" id=\"pdf-container\" style=\"width:100%;overflow: auto;height: 600px;\" v-show=\"!isLoading\" @scroll=\"handleScroll\">\r\n\t\t\t<!-- 导航按钮栏：粘性定位，始终显示在顶部 -->\r\n\t\t\t<div style=\"position:sticky;top:0;z-index:99;background:#fff;padding:10px 0;border-bottom:1px solid #eee;\">\r\n\t\t\t\t<!-- 上一页按钮 -->\r\n\t\t\t\t<el-button size=\"mini\" @click=\"skipPage(curPage - 1)\">上一页</el-button>\r\n\t\t\t\t<!-- 首页按钮 -->\r\n\t\t\t\t<el-button size=\"mini\" @click=\"skipPage(1)\">首页</el-button>\r\n\t\t\t\t<!-- 下一页按钮 -->\r\n\t\t\t\t<el-button size=\"mini\" @click=\"skipPage(curPage + 1)\">下一页</el-button>\r\n\t\t\t\t<!-- 放大当前页按钮 -->\r\n\t\t\t\t<el-button size=\"mini\" @click=\"enlarge(curPage)\">放大</el-button>\r\n\t\t\t\t<!-- 显示当前页码 -->\r\n\t\t\t\t<span style=\"margin-left: 20px; color: #666;\">\r\n\t\t\t\t\t第 {{ curPage }}/{{ totalPages }} 页\r\n\t\t\t\t</span>\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<!-- 直接渲染所有页面，取消虚拟滚动 -->\r\n\t\t\t<div style=\"padding: 10px;\">\r\n\t\t\t\t<div\r\n\t\t\t\t\tv-for=\"pageNum in totalPages\"\r\n\t\t\t\t\t:key=\"pageNum\"\r\n\t\t\t\t\tclass=\"pdf-page\"\r\n\t\t\t\t\tstyle=\"margin-bottom: 20px; text-align: center;\">\r\n\t\t\t\t\t<canvas\r\n\t\t\t\t\t\t:ref=\"`canvas-${pageNum}`\"\r\n\t\t\t\t\t\t:style=\"{ maxWidth: '100%', height: 'auto', border: '1px solid #ddd', borderRadius: '4px' }\"\r\n\t\t\t\t\t\t:data-page=\"pageNum\">\r\n\t\t\t\t\t</canvas>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t\r\n\t\t<!-- PDF放大查看模态框 -->\r\n\t\t<div v-show=\"display_enlarge\" class=\"canvas-enlarge\">\r\n\t\t\t<!-- 关闭按钮：位于右上角 -->\r\n\t\t\t<div style=\"position: absolute; top: 20px; right: 20px; z-index: 1000;\">\r\n\t\t\t\t<el-button type=\"danger\" icon=\"el-icon-close\" circle @click=\"close()\">\r\n\t\t\t\t</el-button>\r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t\t<!-- 放大后的PDF渲染容器：宽度70%，高度56.25rem，支持滚动 -->\r\n\t\t\t<div id=\"enlarge\" style=\"width:70%;height:56.25rem;overflow: auto;\">\r\n\t\t\t\t<!-- 放大模式下的PDF页面渲染 -->\r\n\t\t\t\t<template v-if=\"totalPages > 0\">\r\n\t\t\t\t\t<!-- 遍历每一页PDF，为每页创建放大版本的canvas元素 -->\r\n\t\t\t\t\t<div v-for=\"pageNum in getEnlargeVisiblePages()\" :key=\"`enlarge-${pageNum}`\">\r\n\t\t\t\t\t\t<canvas\r\n\t\t\t\t\t\t\t:ref=\"`enlarge-canvas-${pageNum}`\"\r\n\t\t\t\t\t\t\t:style=\"{ width: '100%', marginBottom: '20px' }\"\r\n\t\t\t\t\t\t\t:data-page=\"pageNum\">\r\n\t\t\t\t\t\t</canvas>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</template>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n// 导入PDF.js库，用于PDF文件的解析和渲染\r\nimport pdfjsLib from \"pdfjs-dist\";\r\nimport axios from 'axios';\r\n// 获取API基础URL，用于拼接完整的PDF文件URL\r\nconst baseURL = process.env.VUE_APP_BASE_API;\r\n\r\nexport default {\r\n\t// 组件名称\r\n\tname: 'PdfViewImproved',\r\n\t\r\n\t// 组件属性定义\r\n\tprops: {\r\n\t\t// PDF文件URL，父组件传入的PDF文件路径\r\n\t\tpdfurl: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: \"\",\r\n\t\t},\r\n\t\t// 唯一标识符，用于区分多个PDF查看器实例，确保DOM元素ID不冲突\r\n\t\tuni_key: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: \"a\",\r\n\t\t},\r\n\t\t// 每页高度（像素），用于虚拟滚动计算\r\n\t\tpageHeight: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 800\r\n\t\t},\r\n\t\t// 缓冲区大小（页数）\r\n\t\tbufferSize: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 3\r\n\t\t}\r\n\t},\r\n\t\r\n\t// 组件数据\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 完整的PDF文件URL（包含baseURL）\r\n\t\t\turl: \"\",\r\n\t\t\t// PDF文档对象\r\n\t\t\tpdfDocument: null,\r\n\t\t\t// 总页数\r\n\t\t\ttotalPages: 0,\r\n\t\t\t// 当前页码\r\n\t\t\tcurPage: 1,\r\n\t\t\t// 是否正在加载PDF\r\n\t\t\tisLoading: false,\r\n\t\t\t// 加载进度百分比（0-100）\r\n\t\t\tloadingProgress: 0,\r\n\t\t\t// 加载详细信息\r\n\t\t\tloadingDetail: \"\",\r\n\t\t\t// 是否显示放大模式\r\n\t\t\tdisplay_enlarge: false,\r\n\t\t\t// 所有页面是否渲染完成\r\n\t\t\tallPagesRendered: false,\r\n\t\t\t\r\n\t\t\t// 页面缓存\r\n\t\t\tpageCache: new Map(),\r\n\t\t\trenderedPages: new Set(),\r\n\t\t\trenderingPages: new Set(),\r\n\t\t\t\r\n\t\t\t// 内存管理\r\n\t\t\tmaxCacheSize: 50, // 最大缓存页数\r\n\t\t};\r\n\t},\r\n\t\r\n\tcomputed: {\r\n\t\t// 移除虚拟滚动相关计算属性\r\n\t},\r\n\t\r\n\twatch: {\r\n\t\t// 监听PDF URL变化\r\n\t\tpdfurl: {\r\n\t\t\thandler(newVal) {\r\n\t\t\t\tif (newVal != null && newVal !== undefined) {\r\n\t\t\t\t\tthis.url = newVal;\r\n\t\t\t\t\tconsole.log(\"this.url\",this.url)\r\n\t\t\t\t\tif (this.url !== \"\") {\r\n\t\t\t\t\t\tthis.url = baseURL + this.url;\r\n\t\t\t\t\t\t// 每次切换PDF都重置状态并回到顶部\r\n\t\t\t\t\t\tthis.resetState();\r\n\t\t\t\t\t\tthis.loadPDF();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timmediate: true\r\n\t\t}\r\n\t\t// 移除visiblePages的watch监听器\r\n\t},\r\n\t\r\n\tmethods: {\r\n\t\t/**\r\n\t\t * 处理窗口大小变化\r\n\t\t */\r\n\t\thandleResize() {\r\n\t\t\tconst container = this.$el.querySelector(\"#pdf-container\");\r\n\t\t\tif (container) {\r\n\t\t\t\tthis.containerHeight = container.clientHeight;\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 格式化进度条显示文本\r\n\t\t */\r\n\t\tformatProgress(percentage) {\r\n\t\t\treturn `${percentage}%`;\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 加载PDF文档 - 支持分片加载\r\n\t\t */\r\n\t\tasync loadPDF() {\r\n\t\t\ttry {\r\n\t\t\t\t// 立即滚动到顶部，在加载开始前\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tconst container = this.$refs.pdfContainer;\r\n\t\t\t\t\tif (container) {\r\n\t\t\t\t\t\tcontainer.scrollTop = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\tthis.loadingProgress = 0;\r\n\t\t\t\tthis.loadingDetail = \"正在初始化PDF加载器...\";\r\n\t\t\t\t\r\n\t\t\t\t// 重置状态\r\n\t\t\t\tthis.resetState();\r\n\t\t\t\t\r\n\t\t\t\t// 配置PDF.js - 修复worker路径问题\r\n\t\t\t\tthis.configurePdfWorker();\r\n\t\t\t\t\r\n\t\t\t\t// 检测是否使用分片加载\r\n\t\t\t\tconst useStreaming = await this.shouldUseStreaming();\r\n\t\t\t\tconsole.log(\"检测是否使用分片加载\",useStreaming)\r\n\t\t\t\tif (useStreaming) {\r\n\t\t\t\t\tawait this.loadPDFWithStreaming();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tawait this.loadPDFStandard();\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error loading PDF:\", error);\r\n\t\t\t\tthis.loadingDetail = \"PDF加载失败：\" + error.message;\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 配置PDF.js worker\r\n\t\t */\r\n\t\tconfigurePdfWorker() {\r\n\t\t\ttry {\r\n\t\t\t\tconst worker = require('pdfjs-dist/build/pdf.worker.min.js');\r\n\t\t\t\tpdfjsLib.GlobalWorkerOptions.workerSrc = worker;\r\n\t\t\t} catch (e) {\r\n\t\t\t\t// 如果require失败，使用CDN\r\n\t\t\t\tpdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 判断是否使用分片加载\r\n\t\t * 直接传入完整文件路径\r\n\t\t */\r\n\t\tasync shouldUseStreaming() {\r\n\t\t\ttry {\r\n\t\t\t\t// 从完整URL中提取文件路径部分\r\n\t\t\t\tconst filePath = this.extractFilePath(this.url);\r\n\t\t\t\tconst response = await axios.get(`${baseURL}/common/pdf/info`, {\r\n\t\t\t\t\tparams: { filePath }\r\n\t\t\t\t});\r\n\t\t\t\tconsole.log(response)\r\n\t\t\t\t// 文件大于5MB时使用分片加载\r\n\t\t\t\treturn response.data.fileSize > 5 * 1024 * 1024;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.warn(\"无法获取文件信息，使用标准加载模式\", error);\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 提取文件路径（从完整URL中提取相对路径）\r\n\t\t */\r\n\t\textractFilePath(url) {\r\n\t\t\t// 从完整URL中提取相对路径部分\r\n\t\t\tconst baseURL = process.env.VUE_APP_BASE_API;\r\n\t\t\tif (url.startsWith(baseURL)) {\r\n\t\t\t\treturn url.substring(baseURL.length);\r\n\t\t\t}\r\n\t\t\t// 如果URL已经是相对路径，直接返回\r\n\t\t\treturn url;\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 提取文件名（保留原有方法用于兼容）\r\n\t\t */\r\n\t\textractFileName(url) {\r\n\t\t\tconst parts = url.split('/');\r\n\t\t\treturn parts[parts.length - 1];\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 分片加载PDF - 真正的HTTP Range分片加载\r\n\t\t * 使用后端/stream接口，支持断点续传和按需加载\r\n\t\t */\r\n\t\tasync loadPDFWithStreaming() {\r\n\t\t\tthis.loadingProgress = 10;\r\n\t\t\tthis.loadingDetail = \"正在初始化分片加载模式...\";\r\n\t\t\t\r\n\t\t\tconst filePath = this.extractFilePath(this.url);\r\n\t\t\tconst streamingUrl = `${baseURL}/common/pdf/stream?filePath=${encodeURIComponent(filePath)}`;\r\n\t\t\t\r\n\t\t\tconsole.log(\"🚀 使用分片加载模式，URL:\", streamingUrl);\r\n\t\t\t\r\n\t\t\t// 分片加载专用配置 - 真正的Range分片\r\n\t\t\tconst loadingTask = pdfjsLib.getDocument({\r\n\t\t\t\turl: streamingUrl,\r\n\t\t\t\trangeChunkSize: 32 * 1024,    // 32KB 更小分片，网络容错更强\r\n\t\t\t\tdisableAutoFetch: true,       // 关键：禁止自动获取，只加载需要的部分\r\n\t\t\t\tdisableStream: false,         // 启用流式传输\r\n\t\t\t\tdisableRange: false,          // 关键：启用Range请求，支持断点续传\r\n\t\t\t\tisEvalSupported: false,       // 安全考虑\r\n\t\t\t\tuseWorkerFetch: true,         // 使用worker处理网络请求\r\n\t\t\t\tmaxImageSize: 10 * 1024 * 1024, // 限制图片大小，防止内存溢出\r\n\t\t\t\tcMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.16.105/cmaps/', // 字体映射\r\n\t\t\t\tcMapPacked: true,\r\n\t\t\t\thttpHeaders: {\r\n\t\t\t\t\t'Accept': 'application/pdf',\r\n\t\t\t\t\t'Cache-Control': 'no-cache',\r\n\t\t\t\t\t'Pragma': 'no-cache',\r\n\t\t\t\t},\r\n\t\t\t\tonProgress: (progressData) => {\r\n\t\t\t\t\t// 分片加载的进度更精确，按块显示\r\n\t\t\t\t\tconst percent = Math.round((progressData.loaded / progressData.total) * 100);\r\n\t\t\t\t\tthis.handleProgress(progressData);\r\n\t\t\t\t\tif (progressData.loaded < progressData.total) {\r\n\t\t\t\t\t\tthis.loadingDetail = `📦 分片下载中... ${percent}% (${Math.round(progressData.loaded / 1024)}KB / ${Math.round(progressData.total / 1024)}KB)`;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.loadingDetail = `✅ 分片下载完成，正在解析...`;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tthis.pdfDocument = await loadingTask.promise;\r\n\t\t\tthis.totalPages = this.pdfDocument.numPages;\r\n\t\t\t\r\n\t\t\tthis.loadingProgress = 100;\r\n\t\t\tthis.loadingDetail = `🎉 PDF分片加载完成！共${this.totalPages}页，内存占用优化`;\r\n\t\t\t\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t// 分片模式下使用按需加载策略\r\n\t\t\t\tthis.startStreamingOptimizedRendering();\r\n\t\t\t}, 500);\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 标准加载模式 - 传统的完整文件下载\r\n\t\t * 直接下载整个PDF文件到内存，适合小文件\r\n\t\t */\r\n\t\tasync loadPDFStandard() {\r\n\t\t\tthis.loadingProgress = 10;\r\n\t\t\tthis.loadingDetail = \"正在标准加载模式...\";\r\n\t\t\t\r\n\t\t\tconsole.log(\"使用标准加载模式，URL:\", this.url);\r\n\t\t\t\r\n\t\t\t// 标准加载专用配置 - 一次性下载整个文件\r\n\t\t\tconst loadingTask = pdfjsLib.getDocument({\r\n\t\t\t\turl: this.url,\r\n\t\t\t\trangeChunkSize: 65536 * 16, // 256KB 大块，减少请求次数\r\n\t\t\t\tdisableAutoFetch: false,    // 允许自动获取\r\n\t\t\t\tdisableStream: true,       // 禁用流式，一次性加载\r\n\t\t\t\tdisableRange: true,        // 禁用Range请求，强制完整下载\r\n\t\t\t\tdisableWorker: false,      // 启用worker处理\r\n\t\t\t\tisEvalSupported: false,\r\n\t\t\t\tuseWorkerFetch: false,\r\n\t\t\t\thttpHeaders: {\r\n\t\t\t\t\t'Accept': 'application/pdf',\r\n\t\t\t\t},\r\n\t\t\t\tonProgress: (progressData) => {\r\n\t\t\t\t\t// 标准加载显示整体进度\r\n\t\t\t\t\tthis.handleProgress(progressData);\r\n\t\t\t\t\tthis.loadingDetail = `完整下载中... ${Math.round(progressData.loaded / 1024)}KB / ${Math.round(progressData.total / 1024)}KB`;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tthis.pdfDocument = await loadingTask.promise;\r\n\t\t\tthis.totalPages = this.pdfDocument.numPages;\r\n\t\t\t\r\n\t\t\tthis.loadingProgress = 100;\r\n\t\t\tthis.loadingDetail = `PDF标准加载完成！共${this.totalPages}页`;\r\n\t\t\t\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t// 标准模式下使用渐进式渲染\r\n\t\t\t\tthis.startOptimizedRendering();\r\n\t\t\t}, 500);\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 处理加载进度\r\n\t\t */\r\n\t\thandleProgress(progressData) {\r\n\t\t\tif (progressData.total > 0) {\r\n\t\t\t\tconst progress = Math.round((progressData.loaded / progressData.total) * 80) + 10;\r\n\t\t\t\tthis.loadingProgress = Math.min(progress, 99);\r\n\t\t\t\tthis.loadingDetail = `正在下载... ${Math.round(progressData.loaded / 1024)}KB / ${Math.round(progressData.total / 1024)}KB`;\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 开始优化渲染（标准模式）\r\n\t\t */\r\n\t\tasync startOptimizedRendering() {\r\n\t\t\tif (!this.pdfDocument || this.totalPages === 0) return;\r\n\t\t\t\r\n\t\t\tconsole.log(`开始优化渲染 ${this.totalPages} 页PDF...`);\r\n\t\t\t\r\n\t\t\t// 分批渲染，避免阻塞UI\r\n\t\t\tconst initialPages = Math.min(5, this.totalPages);\r\n\t\t\tfor (let i = 1; i <= initialPages; i++) {\r\n\t\t\t\tawait this.renderPage(i);\r\n\t\t\t\tawait this.sleep(10);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 延迟加载剩余页面\r\n\t\t\tthis.scheduleLazyRendering();\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 分片模式下的优化渲染（按需加载策略）\r\n\t\t */\r\n\t\tasync startStreamingOptimizedRendering() {\r\n\t\t\tif (!this.pdfDocument || this.totalPages === 0) return;\r\n\t\t\t\r\n\t\t\tconsole.log(`开始分片模式优化渲染 ${this.totalPages} 页PDF...`);\r\n\t\t\t\r\n\t\t\t// 分片模式下使用更激进的按需加载策略\r\n\t\t\tconst initialPages = Math.min(3, this.totalPages);\r\n\t\t\tfor (let i = 1; i <= initialPages; i++) {\r\n\t\t\t\tawait this.renderPage(i);\r\n\t\t\t\tawait this.sleep(5);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 分片模式下使用延迟加载策略，减少初始内存占用\r\n\t\t\tthis.scheduleStreamingLazyRendering();\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 延迟加载剩余页面（标准模式）\r\n\t\t */\r\n\t\tscheduleLazyRendering() {\r\n\t\t\tconst remainingPages = [];\r\n\t\t\tfor (let i = 6; i <= this.totalPages; i++) {\r\n\t\t\t\tremainingPages.push(i);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst renderNextPage = async () => {\r\n\t\t\t\tif (remainingPages.length === 0) return;\r\n\t\t\t\t\r\n\t\t\t\tconst pageNum = remainingPages.shift();\r\n\t\t\t\tif (!this.renderedPages.has(pageNum)) {\r\n\t\t\t\t\tawait this.renderPage(pageNum);\r\n\t\t\t\t\tawait this.sleep(20);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 继续渲染下一页\r\n\t\t\t\tsetTimeout(renderNextPage, 100);\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 延迟开始渲染剩余页面\r\n\t\t\tsetTimeout(renderNextPage, 1000);\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 分片模式下的延迟加载策略（更激进的按需加载）\r\n\t\t */\r\n\t\tscheduleStreamingLazyRendering() {\r\n\t\t\tconst remainingPages = [];\r\n\t\t\tfor (let i = 4; i <= this.totalPages; i++) {\r\n\t\t\t\tremainingPages.push(i);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst renderNextPage = async () => {\r\n\t\t\t\tif (remainingPages.length === 0) {\r\n\t\t\t\t\tthis.checkAllPagesRendered();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 分片模式下更智能的加载策略：优先加载用户附近页面\r\n\t\t\t\tconst nearbyPages = this.getNearbyUnrenderedPages();\r\n\t\t\t\tif (nearbyPages.length > 0) {\r\n\t\t\t\t\tconst pageNum = nearbyPages[0];\r\n\t\t\t\t\tif (!this.renderedPages.has(pageNum) && !this.renderingPages.has(pageNum)) {\r\n\t\t\t\t\t\tawait this.renderPage(pageNum);\r\n\t\t\t\t\t\tawait this.sleep(50);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (remainingPages.length > 0) {\r\n\t\t\t\t\t// 回退到顺序加载\r\n\t\t\t\t\tconst pageNum = remainingPages.shift();\r\n\t\t\t\t\tif (!this.renderedPages.has(pageNum) && !this.renderingPages.has(pageNum)) {\r\n\t\t\t\t\t\tawait this.renderPage(pageNum);\r\n\t\t\t\t\t\tawait this.sleep(30);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 继续渲染下一页，分片模式下间隔更短\r\n\t\t\t\tsetTimeout(renderNextPage, 200);\r\n\t\t\t};\r\n\t\t\t\r\n\t\t\t// 延迟开始渲染剩余页面，分片模式下延迟更短\r\n\t\t\tsetTimeout(renderNextPage, 300);\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 获取当前页面附近的未渲染页面\r\n\t\t */\r\n\t\tgetNearbyUnrenderedPages() {\r\n\t\t\tconst nearbyPages = [];\r\n\t\t\tconst currentPage = this.curPage;\r\n\t\t\tconst range = 2; // 加载当前页前后2页\r\n\t\t\t\r\n\t\t\tfor (let i = Math.max(1, currentPage - range); i <= Math.min(this.totalPages, currentPage + range); i++) {\r\n\t\t\t\tif (!this.renderedPages.has(i) && !this.renderingPages.has(i)) {\r\n\t\t\t\t\tnearbyPages.push(i);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\treturn nearbyPages;\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 重置组件状态\r\n\t\t */\r\n\t\tresetState() {\r\n\t\t\tthis.totalPages = 0;\r\n\t\t\tthis.curPage = 1;\r\n\t\t\tthis.allPagesRendered = false; // 重置渲染完成状态\r\n\t\t\tthis.pageCache.clear();\r\n\t\t\tthis.renderedPages.clear();\r\n\t\t\tthis.renderingPages.clear();\r\n\t\t\tthis.loadingDetail = \"\"; // 清空加载详情\r\n\t\t\t\r\n\t\t\t// 通知父组件渲染状态变化\r\n\t\t\tthis.$emit('render-status-change', false);\r\n\t\t\t\r\n\t\t\t// 清理定时器\r\n\t\t\tif (this._retryTimer) {\r\n\t\t\t\tclearTimeout(this._retryTimer);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 关键：在重置状态时立即滚动到顶部\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tconst container = this.$refs.pdfContainer;\r\n\t\t\t\tif (container) {\r\n\t\t\t\t\tcontainer.scrollTop = 0;\r\n\t\t\t\t\tcontainer.scrollTo(0, 0);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t\r\n\t\t/**\r\n\t\t * 处理滚动事件\r\n\t\t */\r\n\t\thandleScroll(event) {\r\n\t\t\tconst container = event.target;\r\n\t\t\tconst canvases = container.querySelectorAll('canvas[data-page]');\r\n\t\t\t\r\n\t\t\tfor (let i = 0; i < canvases.length; i++) {\r\n\t\t\t\tconst canvas = canvases[i];\r\n\t\t\t\tconst rect = canvas.getBoundingClientRect();\r\n\t\t\t\tconst containerRect = container.getBoundingClientRect();\r\n\t\t\t\t\r\n\t\t\t\t// 如果canvas顶部在容器视口内\r\n\t\t\t\tif (rect.top >= containerRect.top && rect.top <= containerRect.bottom) {\r\n\t\t\t\t\tthis.curPage = parseInt(canvas.getAttribute('data-page'));\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t\r\n\t\t\r\n\t\t/**\r\n\t\t * 渲染所有页面（取消虚拟滚动，解决显示问题）\r\n\t\t */\r\n\t\tasync renderAllPages() {\r\n\t\t\tif (!this.pdfDocument || this.totalPages === 0) return;\r\n\t\t\t\r\n\t\t\tconsole.log(`开始渲染 ${this.totalPages} 页PDF...`);\r\n\t\t\t\r\n\t\t\t// 清空之前的渲染状态\r\n\t\t\tthis.renderedPages.clear();\r\n\t\t\tthis.renderingPages.clear();\r\n\t\t\tthis.allPagesRendered = false;\r\n\t\t\t\r\n\t\t\t// 通知父组件开始渲染\r\n\t\t\tthis.$emit('render-status-change', false);\r\n\t\t\t\r\n\t\t\t// 创建所有页面的占位符\r\n\t\t\tthis.renderedPages = new Set();\r\n\t\t\t\r\n\t\t\t// 批量渲染页面，每批2个页面避免阻塞\r\n\t\t\tconst batchSize = 2;\r\n\t\t\tfor (let i = 1; i <= this.totalPages; i += batchSize) {\r\n\t\t\t\tconst end = Math.min(i + batchSize - 1, this.totalPages);\r\n\t\t\t\tconst promises = [];\r\n\t\t\t\t\r\n\t\t\t\tfor (let pageNum = i; pageNum <= end; pageNum++) {\r\n\t\t\t\t\tpromises.push(this.renderPage(pageNum));\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tawait Promise.all(promises);\r\n\t\t\t\t\r\n\t\t\t\t// 给用户界面更新机会\r\n\t\t\t\tawait this.sleep(50);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 检查是否所有页面都已渲染完成\r\n\t\t\tthis.checkAllPagesRendered();\r\n\t\t\t\r\n\t\t\tconsole.log('所有页面渲染完成');\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 渲染单个页面（简化版）\r\n\t\t */\r\n\t\tasync renderPage(pageNum, isEnlarge = false) {\r\n\t\t\tif (!this.pdfDocument || pageNum < 1 || pageNum > this.totalPages) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.renderingPages.has(pageNum)) return;\r\n\t\t\t\r\n\t\t\tthis.renderingPages.add(pageNum);\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\tconst cacheKey = `${pageNum}-${isEnlarge ? 'enlarge' : 'normal'}`;\r\n\t\t\t\t\r\n\t\t\t\t// 检查缓存\r\n\t\t\t\tif (this.pageCache.has(cacheKey)) {\r\n\t\t\t\t\tthis.renderCanvas(pageNum, this.pageCache.get(cacheKey), isEnlarge);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 获取页面\r\n\t\t\t\tconst page = await this.pdfDocument.getPage(pageNum);\r\n\t\t\t\tconst scale = isEnlarge ? 2 : 1.2;\r\n\t\t\t\tconst viewport = page.getViewport({ scale });\r\n\t\t\t\t\r\n\t\t\t\t// 创建canvas进行渲染\r\n\t\t\t\tconst canvas = document.createElement('canvas');\r\n\t\t\t\tconst context = canvas.getContext('2d');\r\n\t\t\t\t\r\n\t\t\t\tcanvas.height = viewport.height;\r\n\t\t\t\tcanvas.width = viewport.width;\r\n\t\t\t\t\r\n\t\t\t\tawait page.render({\r\n\t\t\t\t\tcanvasContext: context,\r\n\t\t\t\t\tviewport: viewport\r\n\t\t\t\t}).promise;\r\n\t\t\t\t\r\n\t\t\t\t// 缓存渲染结果\r\n\t\t\t\tthis.pageCache.set(cacheKey, canvas);\r\n\t\t\t\t\r\n\t\t\t\t// 渲染到实际canvas\r\n\t\t\t\tthis.renderCanvas(pageNum, canvas, isEnlarge);\r\n\t\t\t\t\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(`渲染第${pageNum}页失败:`, error);\r\n\t\t\t} finally {\r\n\t\t\t\tthis.renderingPages.delete(pageNum);\r\n\t\t\t\tthis.renderedPages.add(pageNum);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 渲染到实际canvas\r\n\t\t */\r\n\t\trenderCanvas(pageNum, sourceCanvas, isEnlarge = false) {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tconst refName = isEnlarge ? `enlarge-canvas-${pageNum}` : `canvas-${pageNum}`;\r\n\t\t\t\tconst canvases = this.$refs[refName];\r\n\t\t\t\t\r\n\t\t\t\t// 确保canvas存在\r\n\t\t\t\tif (!canvases || canvases.length === 0) {\r\n\t\t\t\t\tconsole.warn(`Canvas for page ${pageNum} not found, retrying...`);\r\n\t\t\t\t\tsetTimeout(() => this.renderCanvas(pageNum, sourceCanvas, isEnlarge), 100);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst canvas = canvases[0];\r\n\t\t\t\tif (!canvas) {\r\n\t\t\t\t\tconsole.warn(`Canvas element for page ${pageNum} is null`);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst context = canvas.getContext('2d');\r\n\t\t\t\tif (!context) {\r\n\t\t\t\t\tconsole.warn(`Canvas context for page ${pageNum} is null`);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tcanvas.height = sourceCanvas.height;\r\n\t\t\t\tcanvas.width = sourceCanvas.width;\r\n\t\t\t\t\r\n\t\t\t\tcontext.clearRect(0, 0, canvas.width, canvas.height);\r\n\t\t\t\tcontext.drawImage(sourceCanvas, 0, 0);\r\n\t\t\t\t\r\n\t\t\t\t// 确保页面标记为已渲染\r\n\t\t\t\tthis.renderedPages.add(pageNum);\r\n\t\t\t\t\r\n\t\t\t\t// 检查是否所有页面都已渲染完成\r\n\t\t\t\tthis.checkAllPagesRendered();\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 检查所有页面是否渲染完成\r\n\t\t */\r\n\t\tcheckAllPagesRendered() {\r\n\t\t\tif (this.totalPages > 0 && this.renderedPages.size >= this.totalPages) {\r\n\t\t\t\tthis.allPagesRendered = true;\r\n\t\t\t\tconsole.log('所有PDF页面渲染完成，启用点击事件');\r\n\t\t\t\t// 通知父组件所有页面渲染完成\r\n\t\t\t\tthis.$emit('render-status-change', true);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 管理缓存大小\r\n\t\t */\r\n\t\tmanageCacheSize() {\r\n\t\t\tif (this.pageCache.size > this.maxCacheSize) {\r\n\t\t\t\tconst keys = Array.from(this.pageCache.keys());\r\n\t\t\t\tconst toRemove = keys.slice(0, keys.length - this.maxCacheSize);\r\n\t\t\t\t\r\n\t\t\t\ttoRemove.forEach(key => {\r\n\t\t\t\t\tthis.pageCache.delete(key);\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 跳转到指定页面\r\n\t\t */\r\n\t\tasync skipPage(pageNum) {\r\n\t\t\tif (pageNum < 1) pageNum = 1;\r\n\t\t\tif (pageNum > this.totalPages) pageNum = this.totalPages;\r\n\t\t\t\r\n\t\t\tthis.curPage = pageNum;\r\n\t\t\t\r\n\t\t\t// 滚动到指定页面 - 使用精确的DOM查找\r\n\t\t\tconst container = document.getElementById('pdf-container');\r\n\t\t\tif (!container) {\r\n\t\t\t\tconsole.error('PDF容器未找到');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 使用ref查找canvas元素\r\n\t\t\tconst refName = `canvas-${pageNum}`;\r\n\t\t\tconst targetCanvas = this.$refs[refName];\r\n\t\t\t\r\n\t\t\tif (targetCanvas && targetCanvas[0]) {\r\n\t\t\t\t// 直接滚动到目标canvas\r\n\t\t\t\ttargetCanvas[0].scrollIntoView({\r\n\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\tblock: 'start',\r\n\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\t// 使用querySelector作为备选方案\r\n\t\t\t\tconst canvasSelector = `canvas[data-page=\"${pageNum}\"]`;\r\n\t\t\t\tconst fallbackCanvas = container.querySelector(canvasSelector);\r\n\t\t\t\t\r\n\t\t\t\tif (fallbackCanvas) {\r\n\t\t\t\t\tfallbackCanvas.scrollIntoView({\r\n\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\tblock: 'start',\r\n\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果找不到canvas，直接滚动到对应位置\r\n\t\t\t\t\tconst pageHeight = 800; // 估计的页面高度\r\n\t\t\t\t\tconst targetTop = (pageNum - 1) * pageHeight;\r\n\t\t\t\t\tcontainer.scrollTo({\r\n\t\t\t\t\t\ttop: targetTop,\r\n\t\t\t\t\t\tbehavior: 'smooth'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 放大显示\r\n\t\t * @param {Number} pageNum - 要放大的页码\r\n\t\t */\r\n\t\tasync enlarge(pageNum) {\r\n\t\t\t// 确保当前页码被更新为要放大的页码\r\n\t\t\tthis.curPage = pageNum;\r\n\t\t\t\r\n\t\t\tthis.display_enlarge = true;\r\n\t\t\tdocument.body.style.overflow = \"hidden\";\r\n\t\t\tdocument.documentElement.style.overflow = \"hidden\";\r\n\t\t\t\r\n\t\t\t// 预渲染放大版本（只渲染当前页）\r\n\t\t\tawait this.renderPage(pageNum, true);\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 获取放大模式可见页面\r\n\t\t * 只返回当前页码，实现只放大当前预览的页面\r\n\t\t */\r\n\t\tgetEnlargeVisiblePages() {\r\n\t\t\tif (!this.totalPages) return [];\r\n\t\t\t\r\n\t\t\t// 只返回当前页码\r\n\t\t\treturn [this.curPage];\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 关闭放大模式\r\n\t\t */\r\n\t\tclose() {\r\n\t\t\tthis.display_enlarge = false;\r\n\t\t\tdocument.body.style.overflow = \"auto\";\r\n\t\t\tdocument.documentElement.style.overflow = \"auto\";\r\n\t\t},\r\n\t\t\r\n\t\t/**\r\n\t\t * 工具方法：延时\r\n\t\t */\r\n\t\tsleep(ms) {\r\n\t\t\treturn new Promise(resolve => setTimeout(resolve, ms));\r\n\t\t}\r\n\t},\r\n\t\r\n\tmounted() {\r\n\t\t// 添加窗口大小变化监听\r\n\t\twindow.addEventListener('resize', this.handleResize);\r\n\t},\r\n\t\r\n\tbeforeDestroy() {\r\n\t\t// 清理资源\r\n\t\tthis.pageCache.clear();\r\n\t\tthis.renderedPages.clear();\r\n\t\tthis.renderingPages.clear();\r\n\t\tclearTimeout(this._retryTimer);\r\n\t\twindow.removeEventListener('resize', this.handleResize);\r\n\t},\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.pdf-loading-container {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 100vh;\r\n\tbackground-color: rgba(255, 255, 255, 0.9);\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tz-index: 1000;\r\n}\r\n\r\n.loading-content {\r\n\tbackground: white;\r\n\tpadding: 40px;\r\n\tborder-radius: 12px;\r\n\tbox-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n\ttext-align: center;\r\n\tmin-width: 400px;\r\n\tmax-width: 500px;\r\n}\r\n\r\n.loading-text {\r\n\tfont-size: 18px;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20px;\r\n}\r\n\r\n.loading-progress {\r\n\tmargin: 20px 0;\r\n}\r\n\r\n.loading-detail {\r\n\tfont-size: 14px;\r\n\tcolor: #666;\r\n\tmargin-top: 15px;\r\n\tmin-height: 20px;\r\n}\r\n\r\n.pdf-page {\r\n\tposition: relative;\r\n\tmargin-bottom: 20px;\r\n\ttext-align: center;\r\n}\r\n\r\n.canvas-enlarge {\r\n\tz-index: 999;\r\n\tbackground-color: rgba(0, 0, 0, 0.8);\r\n\tposition: fixed;\r\n\twidth: 100%;\r\n\theight: 100vh;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n\r\n#enlarge {\r\n\tbackground: white;\r\n\tborder-radius: 8px;\r\n\tpadding: 20px;\r\n\tbox-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\r\n}\r\n</style>"]}]}