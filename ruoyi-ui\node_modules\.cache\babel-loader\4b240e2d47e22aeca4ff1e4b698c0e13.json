{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\suppliersRoom.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\suppliersRoom.vue", "mtime": 1753956690842}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\babel.config.js", "mtime": 1750995480034}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_sign", "_interopRequireDefault", "require", "_ready", "_publicity", "_decryption", "_bidAnnouncement", "_end2", "_index", "_info", "_user", "_amount", "_info2", "_quote", "_review", "baseUrl", "process", "env", "VUE_APP_BASE_API", "socketUrl", "replace", "_default", "exports", "default", "components", "Sign", "Ready", "publicity", "decryption", "bidAnnouncement", "end", "data", "node", "userInfo", "projectInfo", "form", "quoteAmount", "projectEvaluationId", "quoteAmountStr", "rules", "required", "message", "trigger", "progress", "itemName", "status", "quoteList", "secondQuoteVisible", "decryptionDeadline", "url", "text_content", "ws", "recordContent", "evaluationInfo", "timer", "isCountdownEnded", "expertList", "intervalId", "countdown", "num", "isLink", "anonymous", "syncedHeight", "currentTime", "computed", "shouldRenderSign", "Object", "keys", "length", "watch", "handler", "_this2", "setTimeout", "element", "document", "getElementById", "console", "log", "offsetHeight", "deep", "methods", "finishSecondQuote", "isOk", "init", "_this3", "promise1", "bidInfo", "bidOpeningTime", "getTodayStartWithDate", "bidOpeningEndTime", "getTodayEndWithDate", "projectId", "$route", "query", "then", "response", "code", "$modal", "msgwarning", "msg", "promise2", "getUserProfile", "localStorage", "setItem", "JSON", "stringify", "promise3", "listInfo", "rows", "$message", "error", "promise4", "expertInfoById", "warning", "Promise", "all", "result", "join", "$refs", "getExpertReviewProgress", "handleStatus", "$store", "getters", "supplierBidOpenStatus", "updateStatus", "head", "getBidStatus", "returnConvertToChineseCurrency", "money", "convertToChineseCurrency", "submitQuote", "_this4", "previousPrice", "project", "budgetAmount", "validate", "valid", "success", "addQuote", "getQuoteList", "catch", "err", "clearInterval", "formatAmount", "amount", "formatBidOpeningTime", "time", "formatDateOption", "formatBidOpeningTimeTwo", "_this5", "queryParams", "pageNum", "pageSize", "params", "entId", "listQuote", "_this6", "approvalProcess", "resultId", "scoringMethodUinfo", "scoringMethodItems", "existingItemNames", "map", "item", "filter", "includes", "evalProjectEvaluationProcess", "find", "startTime", "Date", "setMinutes", "getMinutes", "minutes", "endTime", "toISOString", "split", "handleData", "isAbandonedBid", "_ref", "_this7", "now", "start", "$nextTick", "concat", "VUE_APP_WEBSOCKET_API", "wsurl", "WebSocket", "self", "ws_heartCheck", "timeout", "timeoutObj", "serverTimeoutObj", "_this8", "send", "close", "reset", "clearTimeout", "stop", "onopen", "event", "onmessage", "initdataList", "initChat", "onclose", "exit", "scrollToBottom", "alert", "operateSend", "_this9", "chatHistory", "_this0", "container", "messagesContainer", "scrollTop", "scrollHeight", "headStyle", "_ref2", "row", "column", "rowIndex", "columnIndex", "background", "color", "border", "cellStyle", "_ref3", "startProgressInterval", "_this1", "setInterval", "stopProgressInterval", "updateTime", "ct", "_this", "setSeconds", "getSeconds", "created", "mounted", "updated", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/bidOpeningHall/suppliersRoom.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <BidHeadone ref=\"head\" @updateStatus=\"handleStatus\"></BidHeadone>\r\n    <div class=\"bidOpeningHall\">\r\n      <el-card id=\"main\" class=\"box-card\">\r\n        <div style=\"height: 10px;\">\r\n        <div style=\"padding: 5px 0 0 20px; float: left;\">平台当前时间：<span >{{ currentTime }}</span></div>\r\n        <div style=\"padding: 5px 20px 0 0; float: right;\">\r\n          连接状态：<span :style=\"`color:${isLink ? 'green' : 'red'}`\">{{\r\n            isLink ? \"已连接\" : \"已断连，请刷新重连\"\r\n          }}</span>\r\n        </div></div>\r\n        <Sign ref=\"signIn\" v-if=\"shouldRenderSign\" :projectInfo=\"projectInfo\" :userInfo=\"userInfo\" @sendMessage=\"operateSend\"></Sign>\r\n        <ready ref=\"ready\" v-if=\"node == 'ready' && projectInfo\" :projectInfo=\"projectInfo\"></ready>\r\n        <publicity ref=\"publicity\" v-if=\"node == 'publicity'\"></publicity>\r\n        <decryption ref=\"decryption\" v-if=\"node == 'decryption' && projectInfo\" :projectInfo=\"projectInfo\" :userInfo=\"userInfo\" :deadline=\"decryptionDeadline\" @sendMessage=\"operateSend\"></decryption>\r\n        <bidAnnouncement ref=\"bidAnnouncement\" v-if=\"node == 'bidAnnouncement'\"></bidAnnouncement>\r\n        <end ref=\"end\" v-if=\"node == 'end'\" @sendData=\"handleData\"></end>\r\n\r\n      </el-card>\r\n      <el-card class=\"box-card\" style=\"width: 15%;\">\r\n        <div class=\"im\">\r\n          <div class=\"im-title\">{{ userInfo.ent?userInfo.ent.entName :  ''}}</div>\r\n          <div ref=\"messagesContainer\" class=\"im-content\" :style=\"{height: syncedHeight }\">\r\n            <div v-for=\"(itemc, indexc) in recordContent\" :key=\"indexc\">\r\n              <div class=\"sysMessage\" v-if=\"itemc.type == 0\">\r\n              </div>\r\n              <div v-else>\r\n                <div class=\"word\" v-if=\"itemc.sendId !== userInfo.entId\">\r\n                  <div class=\"info\">\r\n                    <div class=\"message_time\">\r\n                      {{anonymous? \"*******\":itemc.sendName }}\r\n                    </div>\r\n                    <div class=\"info-content\">{{ itemc.content }}</div>\r\n                    <div class=\"message_time\">\r\n                      {{ formatBidOpeningTimeTwo(itemc.sendTime) }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"word-my\" v-else>\r\n                  <div class=\"info\">\r\n                    <div class=\"info-content\">{{ itemc.content }}</div>\r\n                    <div class=\"Sender_time\">\r\n                      {{ formatBidOpeningTimeTwo(itemc.sendTime) }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"im-operation\">\r\n            <div style=\"margin-right:5px\">\r\n              <el-input v-model=\"message\" placeholder=\"输入内容\"></el-input>\r\n            </div>\r\n            <el-button style=\"height: 36px;background: #176ADB;color:#fff\" @click=\"send\">发送</el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <Foot></Foot>\r\n\r\n    <el-dialog :visible.sync=\"secondQuoteVisible\" width=\"60%\">\r\n      <!-- 显示倒计时 -->\r\n      <el-table :data=\"quoteList\" border style=\"width: 100%;\" :cell-style=\"cellStyle\" :header-cell-style=\"headStyle\">\r\n        <el-table-column label=\"二次报价\">\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"100\">\r\n          </el-table-column>\r\n          <el-table-column label=\"历史报价\" prop=\"quoteAmount\" align=\"center\">\r\n          </el-table-column>\r\n          <el-table-column label=\"报价大写\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatAmount(scope.row.quoteAmount) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"报价时间\" prop=\"createTime\" align=\"center\">\r\n          </el-table-column>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div class=\"decryption-countdown\">\r\n        <el-statistic @finish=\"finishSecondQuote\" format=\"二次报价结束倒计时：HH小时mm分ss秒\" :value=\"countdown\" time-indices>\r\n        </el-statistic>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"90px\">\r\n          <el-col :span=\"6\" :offset=\"3\">\r\n            <el-form-item label=\"报价金额：\">\r\n              <el-input v-model=\"form.quoteAmount\" placeholder=\"请输入报价金额\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"7\">\r\n            <el-form-item label=\"大写金额：\">\r\n              <el-input :disabled=\"true\" :value=\"returnConvertToChineseCurrency(form.quoteAmount)\" placeholder=\"请输入报价金额大写\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n        <el-col :span=\"5\">\r\n          <div style=\"display:flex;justify-content: center;align-items: center;\">\r\n            <el-button class=\"quote-button\" :disabled=\"isCountdownEnded\"  @click=\"submitQuote\">确 定</el-button>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Sign from \"./supplierComponent/sign.vue\";\r\nimport Ready from \"./supplierComponent/ready.vue\";\r\nimport publicity from \"./supplierComponent/publicity.vue\";\r\nimport decryption from \"./supplierComponent/decryption.vue\";\r\nimport bidAnnouncement from \"./supplierComponent/bidAnnouncement.vue\";\r\nimport end from \"./supplierComponent/end.vue\";\r\n\r\nimport {\r\n  formatDateOption,\r\n  getTodayStartWithDate,\r\n  getTodayEndWithDate,\r\n} from \"@/utils/index\";\r\nimport { bidInfo, chatHistory } from \"@/api/onlineBidOpening/info\";\r\nimport { getUserProfile } from \"@/api/system/user\";\r\nimport { convertToChineseCurrency } from \"@/utils/amount\";\r\nimport { listInfo } from \"@/api/evaluation/info\";\r\nimport { listQuote, addQuote } from \"@/api/again/quote\";\r\nimport { expertInfoById } from \"@/api/expert/review\";\r\nimport { approvalProcess,inquiringBidList } from \"@/api/expert/review\";\r\n\r\nconst baseUrl = process.env.VUE_APP_BASE_API;\r\nconst socketUrl = baseUrl.replace(\"http\", \"ws\").replace(\"/prod-api\", \"\");\r\nexport default {\r\n  components: { Sign, Ready, publicity, decryption, bidAnnouncement, end },\r\n  data() {\r\n    return {\r\n      node: \"sign\",\r\n      userInfo: {},\r\n      projectInfo: null,\r\n\r\n      // 二次报价表单\r\n      form: {\r\n        quoteAmount: \"\",\r\n        projectEvaluationId: \"\",\r\n        quoteAmountStr: \"\",\r\n      },\r\n      // 二次报价金额\r\n      rules: {\r\n        quoteAmount: [\r\n          { required: true, message: \"请输入金额\", trigger: \"blur\" },\r\n        ],\r\n        quoteAmountStr: [\r\n          { required: true, message: \"请输入金额大写\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      progress: [\r\n        {\r\n          itemName: \"资格性评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"符合性评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"技术标评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"商务标评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"投标报价打分\",\r\n          status: 0,\r\n        },\r\n      ],\r\n      // 二次报价历史报价\r\n      quoteList: [],\r\n      // 显示二次报价弹框\r\n      secondQuoteVisible: false,\r\n\r\n      decryptionDeadline: \"\",\r\n\r\n      url: \"\",\r\n      message: \"\",\r\n      text_content: \"\",\r\n      ws: null,\r\n\r\n      recordContent: [],\r\n      evaluationInfo: {},\r\n      timer: null, // 存储定时器引用\r\n      isCountdownEnded: false,\r\n      // 专家信息\r\n      expertList: {},\r\n      intervalId: null,\r\n      countdown: \"\",\r\n      num: 0,\r\n      isLink: false,\r\n      anonymous: true,\r\n      syncedHeight: '450px', // 初始高度\r\n      currentTime:null\r\n\r\n    };\r\n  },\r\n  computed: {\r\n    shouldRenderSign() {\r\n      return (\r\n        this.node === \"signIn\" &&\r\n        this.projectInfo &&\r\n        this.userInfo &&\r\n        Object.keys(this.userInfo).length > 0\r\n      );\r\n    },\r\n  },\r\n  watch: {\r\n    node: {\r\n      handler() {\r\n        setTimeout(() => {\r\n          var element = document.getElementById('main');\r\n          console.log('element.clientHeight', element.offsetHeight);\r\n          this.syncedHeight = element.offsetHeight - 120 + 'px'\r\n        }, 10);\r\n\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    finishSecondQuote(isOk){\r\n      // this.isCountdownEnded = isOk;\r\n    },\r\n    // 初始化\r\n    init() {\r\n      // 获取开标项目信息\r\n      const promise1 = bidInfo({\r\n        bidOpeningTime: getTodayStartWithDate(),\r\n        bidOpeningEndTime: getTodayEndWithDate(),\r\n        projectId: this.$route.query.projectId,\r\n      }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.projectInfo = response.data;\r\n        } else {\r\n          this.$modal.msgwarning(response.msg);\r\n        }\r\n      });\r\n      // 获取用户信息\r\n      const promise2 = getUserProfile().then((response) => {\r\n        this.userInfo = response.data;\r\n        localStorage.setItem(\"userInfo\", JSON.stringify(this.userInfo));\r\n      });\r\n      // 获取项目评审信息\r\n      const promise3 = listInfo({\r\n        projectId: this.$route.query.projectId,\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          if (response.rows && response.rows.length > 0) {\r\n            this.evaluationInfo = response.rows[response.rows.length - 1];\r\n          } else {\r\n            this.evaluationInfo = {};\r\n            // this.$message.error(\"未查询到项目评审信息\");\r\n          }\r\n        } else {\r\n          this.$message.error(response.msg);\r\n        }\r\n      });\r\n      // 获取专家信息\r\n      const promise4 = expertInfoById({\r\n        projectId: this.$route.query.projectId,\r\n      }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.expertList = response.data;\r\n          localStorage.setItem(\"expertList\", JSON.stringify(this.expertList));\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n\r\n      Promise.all([promise1, promise2, promise3, promise4]).then((result) => {\r\n        this.join();\r\n        if (this.node == \"end\") {\r\n          this.$refs.end.getExpertReviewProgress();\r\n        }\r\n      });\r\n    },\r\n    // 获取当前开标室流程\r\n    handleStatus(data) {\r\n      this.anonymous = this.$store.getters.supplierBidOpenStatus >= 2 ? false : true;\r\n      switch (data) {\r\n        case \"签到\":\r\n          this.node = \"signIn\";\r\n          break;\r\n        case \"开标准备\":\r\n          this.node = \"ready\";\r\n          break;\r\n        case \"投标人公示\":\r\n          this.node = \"publicity\";\r\n          break;\r\n        case \"标书解密\":\r\n          this.node = \"decryption\";\r\n          break;\r\n        case \"唱标\":\r\n          this.node = \"bidAnnouncement\";\r\n          break;\r\n        case \"开标结束\":\r\n          this.node = \"end\";\r\n          break;\r\n      }\r\n    },\r\n    // 节点更新通知\r\n    updateStatus() {\r\n      this.$refs.head.getBidStatus();\r\n    },\r\n\r\n    // 转换大写的报价金额\r\n    returnConvertToChineseCurrency(money) {\r\n      return convertToChineseCurrency(money);\r\n    },\r\n    // 提交报价\r\n    submitQuote() {\r\n      if (this.isCountdownEnded) {\r\n        this.$message.warning(\"倒计时结束，禁止报价\");\r\n        this.secondQuoteVisible = false;\r\n        return;\r\n      }\r\n      var previousPrice = 0;\r\n      \r\n      if(this.quoteList.length===0){\r\n        previousPrice = this.projectInfo.project.budgetAmount;\r\n      }else{\r\n        previousPrice = this.quoteList[this.quoteList.length-1].quoteAmount;\r\n      }\r\n      if(this.form.quoteAmount>previousPrice){\r\n        this.$message.error(\"本次报价不能超过上次报价 \"+ previousPrice + \" 元\");\r\n      }\r\n      //提交报价\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          if (this.evaluationInfo.projectEvaluationId == null) {\r\n            listInfo({\r\n              projectId: this.$route.query.projectId,\r\n            }).then((response) => {\r\n              if (response.code === 200) {\r\n                if (response.rows && response.rows.length > 0) {\r\n                  this.evaluationInfo = response.rows[response.rows.length - 1];\r\n                  this.form.projectEvaluationId = this.evaluationInfo.projectEvaluationId;\r\n                } else {\r\n                  this.$message.error(\"没有评审信息！\");\r\n                  return\r\n                }\r\n              } else {\r\n                this.$message.success(response.msg);\r\n              }\r\n            });\r\n          } else {\r\n            this.form.projectEvaluationId = this.evaluationInfo.projectEvaluationId;\r\n          }\r\n          addQuote(this.form)\r\n            .then((result) => {\r\n              if (result.code === 200) {\r\n                this.$message.success(result.msg);\r\n                this.getQuoteList();\r\n                this.secondQuoteVisible = false;\r\n              } else {\r\n                this.$message.error(result.msg);\r\n              }\r\n            })\r\n            .catch((err) => { });\r\n        } else {\r\n          console.log(\"error submit!!\");\r\n          return false;\r\n        }\r\n      });\r\n      // 关闭弹窗时，清除定时器\r\n      if (this.timer) {\r\n        clearInterval(this.timer);\r\n      }\r\n    },\r\n    // 转换大写的报价金额\r\n    formatAmount(amount) {\r\n      return convertToChineseCurrency(amount);\r\n    },\r\n\r\n    // 格式化开标时间显示\r\n    formatBidOpeningTime(time) {\r\n      return formatDateOption(time, \"date\");\r\n    },\r\n    // 格式化开标时间显示 时-分-秒\r\n    formatBidOpeningTimeTwo(time) {\r\n      return formatDateOption(time, \"time\");\r\n    },\r\n    // 获取历史报价\r\n    getQuoteList() {\r\n      const queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 999,\r\n        params: {\r\n          projectId: this.$route.query.projectId,\r\n          entId: this.userInfo.entId,\r\n        },\r\n      };\r\n      listQuote(queryParams)\r\n        .then((result) => {\r\n          //存入数据\r\n          this.quoteList = result.rows;\r\n          console.log(this.quoteList.length + 1 == this.num);\r\n          if (this.quoteList.length + 1 == this.num) {\r\n            this.secondQuoteVisible = true;\r\n            // this.isCountdownEnded = true;\r\n          } else {\r\n            // this.isCountdownEnded = false;\r\n            this.secondQuoteVisible = false;\r\n          }\r\n        })\r\n        .catch((err) => { });\r\n    },\r\n    getExpertReviewProgress() {\r\n      approvalProcess(this.$route.query.projectId, this.expertList[0].resultId).then(\r\n        (response) => {\r\n          if (response.code == 200 && response.data.scoringMethodUinfo.scoringMethodItems) {\r\n            \r\n            const existingItemNames = response.data.scoringMethodUinfo.scoringMethodItems.map(item => item.itemName);\r\n            this.progress = this.progress.filter(item => existingItemNames.includes(item.itemName));\r\n\r\n            const evalProjectEvaluationProcess = this.progress.find((item) => {\r\n              return item.itemName == \"投标报价打分\";\r\n            }).evalProjectEvaluationProcess;\r\n            if (evalProjectEvaluationProcess) {\r\n              let startTime = new Date(\r\n                evalProjectEvaluationProcess.startTime.replace(\" \", \"T\") + \"Z\"\r\n              );\r\n              // 将 Date 对象加上30秒\r\n              startTime.setMinutes(startTime.getMinutes() + evalProjectEvaluationProcess.minutes);\r\n              var endTime = startTime\r\n                .toISOString()\r\n                .replace(\"T\", \" \")\r\n                .replace(\"Z\", \"\")\r\n                .split(\".\")[0];\r\n              // 将更新后的时间转换回字符串格式\r\n              this.num = evalProjectEvaluationProcess.num;\r\n              this.handleData({\r\n                startTime: evalProjectEvaluationProcess.startTime,\r\n                endTime: endTime,\r\n                num: this.num,\r\n                isAbandonedBid: 0\r\n              });\r\n            }\r\n          } else {\r\n\r\n          }\r\n        }\r\n      );\r\n\r\n    },\r\n    // 判断是否打开二次报价弹窗\r\n    handleData({ startTime, endTime, num, isAbandonedBid }) {\r\n      if (this.secondQuoteVisible == true) {\r\n        return;\r\n      } else {\r\n        const now = new Date(); // 当前时间\r\n        const start = new Date(startTime); // 开始时间\r\n        const end = new Date(endTime); // 结束时间\r\n        console.log(\"isAbandonedBid\",isAbandonedBid);\r\n        \r\n        if (now >= start && now <= end && isAbandonedBid==0) {\r\n          this.$nextTick(() => {\r\n            this.countdown = new Date(endTime);\r\n          });\r\n          this.num = num;\r\n          console.log(\"this.num\", this.num);\r\n          this.form = {};\r\n          this.getQuoteList();\r\n        } else {\r\n          this.secondQuoteVisible = false;\r\n        }\r\n      }\r\n    },\r\n    // 连接websocket\r\n    join() {\r\n      // this.url = `${socketUrl}/websocket/message/${this.userInfo.entId}/${this.$route.query.projectId}/0`;\r\n      this.url = `${process.env.VUE_APP_WEBSOCKET_API}/websocket/message/${this.userInfo.entId}/${this.$route.query.projectId}/0`;\r\n      const wsurl = this.url;\r\n      this.ws = new WebSocket(wsurl);\r\n      const self = this;\r\n      // 心跳检测函数\r\n      const ws_heartCheck = {\r\n        timeout: 5000, // 5秒\r\n        timeoutObj: null,\r\n        serverTimeoutObj: null,\r\n        start: function () {\r\n          this.timeoutObj = setTimeout(() => {\r\n            // 这里发送一个心跳包\r\n            self.ws.send(\"ping\");\r\n            this.serverTimeoutObj = setTimeout(() => {\r\n              self.ws.close(); // 如果超过一定时间还没重置，视为断开连接\r\n            }, this.timeout);\r\n          }, this.timeout);\r\n        },\r\n        reset: function () {\r\n          clearTimeout(this.timeoutObj);\r\n          clearTimeout(this.serverTimeoutObj);\r\n          this.start();\r\n        },\r\n        stop: function () {\r\n          clearTimeout(this.timeoutObj);\r\n          clearTimeout(this.serverTimeoutObj);\r\n        }\r\n      };\r\n      this.ws.onopen = function (event) {\r\n        ws_heartCheck.start();\r\n        self.text_content = self.text_content + \"已经打开开标室连接!\" + \"\\n\";\r\n        self.isLink = true;\r\n\r\n        console.log(self.text_content);\r\n      };\r\n      this.ws.onmessage = function (event) {\r\n        console.log(event.data);\r\n        if(event.data == \"ping\"){\r\n          // 心跳响应\r\n          ws_heartCheck.reset();\r\n        }else if (event.data == \"连接成功\") {\r\n          console.log(\"socketUrl\", socketUrl);\r\n        } else if (event.data == \"signIn\") {\r\n          self.updateStatus();\r\n        } else if (event.data == \"bidPublicity\") {\r\n          self.updateStatus();\r\n        } else if (event.data.includes(\"decryption\")) {\r\n          self.updateStatus();\r\n        } else if (event.data == \"supDecrytion\") {\r\n          self.$refs.decryption.initdataList();\r\n        } else if (event.data == \"nextStep\") {\r\n          // 不做任何操作\r\n        } else if (\r\n          event.data == \"bidAnnouncement\" ||\r\n          event.data == \"flowLabel\"\r\n        ) {\r\n          self.updateStatus();\r\n        } else if (event.data == \"end\") {\r\n          self.updateStatus();\r\n        } else if (event.data == \"evalAgainQuote\") {\r\n          self.getExpertReviewProgress();\r\n          // //打开二次报价弹窗\r\n          // self.form = {};\r\n          // //加载报价记录\r\n          // self.getQuoteList();\r\n        } else {\r\n          self.initChat();\r\n        }\r\n      };\r\n      this.ws.onclose = function (event) {\r\n        self.text_content = self.text_content + \"已经关闭开标室连接!\" + \"\\n\";\r\n        self.isLink = false;\r\n        console.log(self.text_content);\r\n        //断开后自动重连\r\n        ws_heartCheck.stop();\r\n        self.join();\r\n      };\r\n    },\r\n    // 断开websocket连接\r\n    exit() {\r\n      if (this.ws) {\r\n        this.ws.close();\r\n        this.ws = null;\r\n      }\r\n    },\r\n    // 发送消息\r\n    send() {\r\n      if (this.ws) {\r\n        this.ws.send(this.message);\r\n        this.message = \"\";\r\n        this.scrollToBottom();\r\n      } else {\r\n        alert(\"未连接到开标室服务器\");\r\n      }\r\n    },\r\n    // 发送消息\r\n    operateSend(message) {\r\n      if (this.ws) {\r\n        this.ws.send(message);\r\n      } else {\r\n        alert(\"未连接到开标室服务器\");\r\n      }\r\n    },\r\n    // 初始化聊天记录\r\n    initChat() {\r\n      chatHistory(this.$route.query.projectId).then((response) => {\r\n        this.recordContent = response.data;\r\n      });\r\n    },\r\n    // 处理滚动\r\n    scrollToBottom() {\r\n      this.$nextTick(() => {\r\n        const container = this.$refs.messagesContainer;\r\n        container.scrollTop = container.scrollHeight;\r\n      });\r\n    },\r\n    // 表格头颜色\r\n    headStyle({ row, column, rowIndex, columnIndex }) {\r\n      console.log(row, column, rowIndex, columnIndex);\r\n      if (rowIndex === 0 && columnIndex === 0) {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          background: \"#1C57A7\",\r\n          color: \"#fff\",\r\n          \"font-size\": \"16px\",\r\n          \"font-weight\": \"700\",\r\n        };\r\n      } else {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          background: \"#176ADB\",\r\n          color: \"#fff\",\r\n          \"font-size\": \"16px\",\r\n          \"font-weight\": \"700\",\r\n          border: \"0px\",\r\n        };\r\n      }\r\n    },\r\n    // 表格样式\r\n    cellStyle({ row, column, rowIndex, columnIndex }) {\r\n      if (rowIndex % 2 === 0) {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          \"font-weight\": \"700\",\r\n          color: \"#000\",\r\n          \"font-size\": \"14px\",\r\n          background: \"#FFFFFF\",\r\n        };\r\n      } else {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          \"font-weight\": \"700\",\r\n          color: \"#000\",\r\n          \"font-size\": \"14px\",\r\n          background: \"#F5F5F5\",\r\n        };\r\n      }\r\n    },\r\n    startProgressInterval() {\r\n      this.intervalId = setInterval(() => {\r\n        if (this.node == \"end\") {\r\n          this.$refs.end.getExpertReviewProgress();\r\n          //在这里增加 供应商消息刷新页面接口\r\n\r\n        }\r\n      }, 5000); // 每隔 5 秒调用一次\r\n    },\r\n    stopProgressInterval() {\r\n      clearInterval(this.intervalId);\r\n    },\r\n    updateTime() {\r\n      var ct = new Date();\r\n      var _this = this;\r\n\r\n      setInterval(function(){\r\n        ct.setSeconds(ct.getSeconds() + 1);\r\n        _this.currentTime = formatDateOption(ct, \"cdatetime\");\r\n      }, 1000); // 每秒更新时间\r\n    }\r\n  },\r\n  created() { },\r\n  mounted() {\r\n    this.init();\r\n    this.initChat();\r\n    // 在组件挂载后启动定时器\r\n    this.startProgressInterval();\r\n    this.updateTime();\r\n  },\r\n  updated() {\r\n    this.scrollToBottom();\r\n  },\r\n  beforeDestroy() {\r\n    // 在组件销毁前清除定时器\r\n    this.stopProgressInterval();\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .el-card__body {\r\n  padding: 0;\r\n}\r\n</style>\r\n\r\n<style scoped lang=\"scss\">\r\n.bidOpeningHall {\r\n  position: relative;\r\n  background-color: #f5f5f5;\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n  justify-content: center;\r\n  align-content: flex-start;\r\n  align-items: flex-start;\r\n}\r\n.box-card {\r\n  min-height: 600px;\r\n  width: 50%;\r\n  margin: 15px 5px;\r\n}\r\n.im {\r\n  .im-title {\r\n    width: 100%;\r\n    height: 50px;\r\n    background: #176adb;\r\n\r\n    font-weight: 500;\r\n    font-size: 16px;\r\n    color: #ffffff;\r\n    letter-spacing: 0;\r\n\r\n    line-height: 50px;\r\n    text-align: center;\r\n  }\r\n  .im-content {\r\n    margin: 10px;\r\n    background: #f5f5f5;\r\n    // height: 450px;\r\n    overflow-y: auto;\r\n  }\r\n  .im-operation {\r\n    display: flex;\r\n    margin: 0 10px;\r\n    margin-bottom: 10px;\r\n    overflow: auto;\r\n  }\r\n}\r\n.im-content {\r\n  .word {\r\n    display: flex;\r\n    margin-bottom: 30px;\r\n\r\n    img {\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 50%;\r\n    }\r\n    .info {\r\n      width: 47%;\r\n      margin-left: 10px;\r\n      .Sender_time {\r\n        padding-right: 12px;\r\n        padding-top: 5px;\r\n        font-size: 12px;\r\n        color: rgba(51, 51, 51, 0.8);\r\n        margin: 0;\r\n        height: 20px;\r\n      }\r\n      .message_time {\r\n        font-size: 12px;\r\n        color: rgba(51, 51, 51, 0.8);\r\n        margin: 0;\r\n        height: 20px;\r\n        line-height: 20px;\r\n        margin-top: -5px;\r\n        margin-top: 5px;\r\n      }\r\n      .info-content {\r\n        word-break: break-all;\r\n        // max-width: 45%;\r\n        display: inline-block;\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        background: #fff;\r\n        position: relative;\r\n        margin-top: 8px;\r\n        background: #dbdbdb;\r\n        border-radius: 4px;\r\n      }\r\n      //小三角形\r\n      .info-content::before {\r\n        position: absolute;\r\n        left: -8px;\r\n        top: 8px;\r\n        content: \"\";\r\n        border-right: 10px solid #dbdbdb;\r\n        border-top: 8px solid transparent;\r\n        border-bottom: 8px solid transparent;\r\n      }\r\n    }\r\n  }\r\n\r\n  .word-my {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    // margin-bottom: 30px;\r\n    img {\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 50%;\r\n    }\r\n    .info {\r\n      width: 90%;\r\n      // margin-left: 10px;\r\n      text-align: right;\r\n      // position: relative;\r\n      display: flex;\r\n      align-items: flex-end;\r\n      flex-wrap: wrap;\r\n      flex-direction: column;\r\n      .info-content {\r\n        word-break: break-all;\r\n        max-width: 45%;\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        // float: right;\r\n        margin-right: 10px;\r\n        position: relative;\r\n        margin-top: 8px;\r\n        background: #a3c3f6;\r\n        text-align: left;\r\n        border-radius: 4px;\r\n      }\r\n      .Sender_time {\r\n        padding-right: 12px;\r\n        padding-top: 5px;\r\n        font-size: 12px;\r\n        color: rgba(51, 51, 51, 0.8);\r\n        margin: 0;\r\n        height: 20px;\r\n      }\r\n      //小三角形\r\n      .info-content::after {\r\n        position: absolute;\r\n        right: -8px;\r\n        top: 8px;\r\n        content: \"\";\r\n        border-left: 10px solid #a3c3f6;\r\n        border-top: 8px solid transparent;\r\n        border-bottom: 8px solid transparent;\r\n      }\r\n    }\r\n  }\r\n}\r\n.countdown-timer {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #fff;\r\n  margin: 30px 0;\r\n\r\n  width: 30%;\r\n  height: 70px;\r\n\r\n  background: #176adb;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n.quote-button {\r\n  width: 100px;\r\n  background: #176adb;\r\n\r\n  color: #fff;\r\n  font-weight: 700;\r\n\r\n  border-radius: 5px;\r\n}\r\n.exper-title {\r\n  height: 45px;\r\n  background: #176adb;\r\n  display: flex;\r\n  justify-content: center;\r\n\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 16px;\r\n  color: #ffffff;\r\n  letter-spacing: 0;\r\n  align-items: center;\r\n}\r\n.expert-title-second {\r\n  height: 40px;\r\n  background: #1c57a7;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n.decryption-countdown {\r\n  width: 50%;\r\n  height: 105px;\r\n  margin: 10px 25%;\r\n\r\n  // background: #176adb;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  ::v-deep .number {\r\n    color: #000;\r\n    font-weight: 700;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA4GA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,UAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,WAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,gBAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,KAAA,GAAAN,sBAAA,CAAAC,OAAA;AAEA,IAAAM,MAAA,GAAAN,OAAA;AAKA,IAAAO,KAAA,GAAAP,OAAA;AACA,IAAAQ,KAAA,GAAAR,OAAA;AACA,IAAAS,OAAA,GAAAT,OAAA;AACA,IAAAU,MAAA,GAAAV,OAAA;AACA,IAAAW,MAAA,GAAAX,OAAA;AACA,IAAAY,OAAA,GAAAZ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAAa,OAAA,GAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;AACA,IAAAC,SAAA,GAAAJ,OAAA,CAAAK,OAAA,eAAAA,OAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GACA;EACAC,UAAA;IAAAC,IAAA,EAAAA,aAAA;IAAAC,KAAA,EAAAA,cAAA;IAAAC,SAAA,EAAAA,kBAAA;IAAAC,UAAA,EAAAA,mBAAA;IAAAC,eAAA,EAAAA,wBAAA;IAAAC,GAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,QAAA;MACAC,WAAA;MAEA;MACAC,IAAA;QACAC,WAAA;QACAC,mBAAA;QACAC,cAAA;MACA;MACA;MACAC,KAAA;QACAH,WAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,cAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,QAAA,GACA;QACAC,QAAA;QACAC,MAAA;MACA,GACA;QACAD,QAAA;QACAC,MAAA;MACA,GACA;QACAD,QAAA;QACAC,MAAA;MACA,GACA;QACAD,QAAA;QACAC,MAAA;MACA,GACA;QACAD,QAAA;QACAC,MAAA;MACA,EACA;MACA;MACAC,SAAA;MACA;MACAC,kBAAA;MAEAC,kBAAA;MAEAC,GAAA;MACAR,OAAA;MACAS,YAAA;MACAC,EAAA;MAEAC,aAAA;MACAC,cAAA;MACAC,KAAA;MAAA;MACAC,gBAAA;MACA;MACAC,UAAA;MACAC,UAAA;MACAC,SAAA;MACAC,GAAA;MACAC,MAAA;MACAC,SAAA;MACAC,YAAA;MAAA;MACAC,WAAA;IAEA;EACA;EACAC,QAAA;IACAC,gBAAA,WAAAA,iBAAA;MACA,OACA,KAAAjC,IAAA,iBACA,KAAAE,WAAA,IACA,KAAAD,QAAA,IACAiC,MAAA,CAAAC,IAAA,MAAAlC,QAAA,EAAAmC,MAAA;IAEA;EACA;EACAC,KAAA;IACArC,IAAA;MACAsC,OAAA,WAAAA,QAAA;QAAA,IAAAC,MAAA;QACAC,UAAA;UACA,IAAAC,OAAA,GAAAC,QAAA,CAAAC,cAAA;UACAC,OAAA,CAAAC,GAAA,yBAAAJ,OAAA,CAAAK,YAAA;UACAP,MAAA,CAAAT,YAAA,GAAAW,OAAA,CAAAK,YAAA;QACA;MAEA;MACAC,IAAA;IACA;EACA;EACAC,OAAA;IACAC,iBAAA,WAAAA,kBAAAC,IAAA;MACA;IAAA,CACA;IACA;IACAC,IAAA,WAAAA,KAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,QAAA,OAAAC,aAAA;QACAC,cAAA,MAAAC,4BAAA;QACAC,iBAAA,MAAAC,0BAAA;QACAC,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF;MACA,GAAAG,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAZ,MAAA,CAAAlD,WAAA,GAAA6D,QAAA,CAAAhE,IAAA;QACA;UACAqD,MAAA,CAAAa,MAAA,CAAAC,UAAA,CAAAH,QAAA,CAAAI,GAAA;QACA;MACA;MACA;MACA,IAAAC,QAAA,OAAAC,oBAAA,IAAAP,IAAA,WAAAC,QAAA;QACAX,MAAA,CAAAnD,QAAA,GAAA8D,QAAA,CAAAhE,IAAA;QACAuE,YAAA,CAAAC,OAAA,aAAAC,IAAA,CAAAC,SAAA,CAAArB,MAAA,CAAAnD,QAAA;MACA;MACA;MACA,IAAAyE,QAAA,OAAAC,eAAA;QACAhB,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF;MACA,GAAAG,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACA,IAAAD,QAAA,CAAAa,IAAA,IAAAb,QAAA,CAAAa,IAAA,CAAAxC,MAAA;YACAgB,MAAA,CAAA/B,cAAA,GAAA0C,QAAA,CAAAa,IAAA,CAAAb,QAAA,CAAAa,IAAA,CAAAxC,MAAA;UACA;YACAgB,MAAA,CAAA/B,cAAA;YACA;UACA;QACA;UACA+B,MAAA,CAAAyB,QAAA,CAAAC,KAAA,CAAAf,QAAA,CAAAI,GAAA;QACA;MACA;MACA;MACA,IAAAY,QAAA,OAAAC,sBAAA;QACArB,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF;MACA,GAAAG,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAZ,MAAA,CAAA5B,UAAA,GAAAuC,QAAA,CAAAhE,IAAA;UACAuE,YAAA,CAAAC,OAAA,eAAAC,IAAA,CAAAC,SAAA,CAAArB,MAAA,CAAA5B,UAAA;QACA;UACA4B,MAAA,CAAAyB,QAAA,CAAAI,OAAA,CAAAlB,QAAA,CAAAI,GAAA;QACA;MACA;MAEAe,OAAA,CAAAC,GAAA,EAAA9B,QAAA,EAAAe,QAAA,EAAAM,QAAA,EAAAK,QAAA,GAAAjB,IAAA,WAAAsB,MAAA;QACAhC,MAAA,CAAAiC,IAAA;QACA,IAAAjC,MAAA,CAAApD,IAAA;UACAoD,MAAA,CAAAkC,KAAA,CAAAxF,GAAA,CAAAyF,uBAAA;QACA;MACA;IACA;IACA;IACAC,YAAA,WAAAA,aAAAzF,IAAA;MACA,KAAA8B,SAAA,QAAA4D,MAAA,CAAAC,OAAA,CAAAC,qBAAA;MACA,QAAA5F,IAAA;QACA;UACA,KAAAC,IAAA;UACA;QACA;UACA,KAAAA,IAAA;UACA;QACA;UACA,KAAAA,IAAA;UACA;QACA;UACA,KAAAA,IAAA;UACA;QACA;UACA,KAAAA,IAAA;UACA;QACA;UACA,KAAAA,IAAA;UACA;MACA;IACA;IACA;IACA4F,YAAA,WAAAA,aAAA;MACA,KAAAN,KAAA,CAAAO,IAAA,CAAAC,YAAA;IACA;IAEA;IACAC,8BAAA,WAAAA,+BAAAC,KAAA;MACA,WAAAC,gCAAA,EAAAD,KAAA;IACA;IACA;IACAE,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,SAAA5E,gBAAA;QACA,KAAAsD,QAAA,CAAAI,OAAA;QACA,KAAAlE,kBAAA;QACA;MACA;MACA,IAAAqF,aAAA;MAEA,SAAAtF,SAAA,CAAAsB,MAAA;QACAgE,aAAA,QAAAlG,WAAA,CAAAmG,OAAA,CAAAC,YAAA;MACA;QACAF,aAAA,QAAAtF,SAAA,MAAAA,SAAA,CAAAsB,MAAA,MAAAhC,WAAA;MACA;MACA,SAAAD,IAAA,CAAAC,WAAA,GAAAgG,aAAA;QACA,KAAAvB,QAAA,CAAAC,KAAA,mBAAAsB,aAAA;MACA;MACA;MACA,KAAAd,KAAA,CAAAnF,IAAA,CAAAoG,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAL,MAAA,CAAA9E,cAAA,CAAAhB,mBAAA;YACA,IAAAsE,eAAA;cACAhB,SAAA,EAAAwC,MAAA,CAAAvC,MAAA,CAAAC,KAAA,CAAAF;YACA,GAAAG,IAAA,WAAAC,QAAA;cACA,IAAAA,QAAA,CAAAC,IAAA;gBACA,IAAAD,QAAA,CAAAa,IAAA,IAAAb,QAAA,CAAAa,IAAA,CAAAxC,MAAA;kBACA+D,MAAA,CAAA9E,cAAA,GAAA0C,QAAA,CAAAa,IAAA,CAAAb,QAAA,CAAAa,IAAA,CAAAxC,MAAA;kBACA+D,MAAA,CAAAhG,IAAA,CAAAE,mBAAA,GAAA8F,MAAA,CAAA9E,cAAA,CAAAhB,mBAAA;gBACA;kBACA8F,MAAA,CAAAtB,QAAA,CAAAC,KAAA;kBACA;gBACA;cACA;gBACAqB,MAAA,CAAAtB,QAAA,CAAA4B,OAAA,CAAA1C,QAAA,CAAAI,GAAA;cACA;YACA;UACA;YACAgC,MAAA,CAAAhG,IAAA,CAAAE,mBAAA,GAAA8F,MAAA,CAAA9E,cAAA,CAAAhB,mBAAA;UACA;UACA,IAAAqG,eAAA,EAAAP,MAAA,CAAAhG,IAAA,EACA2D,IAAA,WAAAsB,MAAA;YACA,IAAAA,MAAA,CAAApB,IAAA;cACAmC,MAAA,CAAAtB,QAAA,CAAA4B,OAAA,CAAArB,MAAA,CAAAjB,GAAA;cACAgC,MAAA,CAAAQ,YAAA;cACAR,MAAA,CAAApF,kBAAA;YACA;cACAoF,MAAA,CAAAtB,QAAA,CAAAC,KAAA,CAAAM,MAAA,CAAAjB,GAAA;YACA;UACA,GACAyC,KAAA,WAAAC,GAAA;QACA;UACAjE,OAAA,CAAAC,GAAA;UACA;QACA;MACA;MACA;MACA,SAAAvB,KAAA;QACAwF,aAAA,MAAAxF,KAAA;MACA;IACA;IACA;IACAyF,YAAA,WAAAA,aAAAC,MAAA;MACA,WAAAf,gCAAA,EAAAe,MAAA;IACA;IAEA;IACAC,oBAAA,WAAAA,qBAAAC,IAAA;MACA,WAAAC,uBAAA,EAAAD,IAAA;IACA;IACA;IACAE,uBAAA,WAAAA,wBAAAF,IAAA;MACA,WAAAC,uBAAA,EAAAD,IAAA;IACA;IACA;IACAP,YAAA,WAAAA,aAAA;MAAA,IAAAU,MAAA;MACA,IAAAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;UACA9D,SAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,SAAA;UACA+D,KAAA,OAAAzH,QAAA,CAAAyH;QACA;MACA;MACA,IAAAC,gBAAA,EAAAL,WAAA,EACAxD,IAAA,WAAAsB,MAAA;QACA;QACAiC,MAAA,CAAAvG,SAAA,GAAAsE,MAAA,CAAAR,IAAA;QACAhC,OAAA,CAAAC,GAAA,CAAAwE,MAAA,CAAAvG,SAAA,CAAAsB,MAAA,QAAAiF,MAAA,CAAA1F,GAAA;QACA,IAAA0F,MAAA,CAAAvG,SAAA,CAAAsB,MAAA,QAAAiF,MAAA,CAAA1F,GAAA;UACA0F,MAAA,CAAAtG,kBAAA;UACA;QACA;UACA;UACAsG,MAAA,CAAAtG,kBAAA;QACA;MACA,GACA6F,KAAA,WAAAC,GAAA;IACA;IACAtB,uBAAA,WAAAA,wBAAA;MAAA,IAAAqC,MAAA;MACA,IAAAC,uBAAA,OAAAjE,MAAA,CAAAC,KAAA,CAAAF,SAAA,OAAAnC,UAAA,IAAAsG,QAAA,EAAAhE,IAAA,CACA,UAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA,WAAAD,QAAA,CAAAhE,IAAA,CAAAgI,kBAAA,CAAAC,kBAAA;UAEA,IAAAC,iBAAA,GAAAlE,QAAA,CAAAhE,IAAA,CAAAgI,kBAAA,CAAAC,kBAAA,CAAAE,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAvH,QAAA;UAAA;UACAgH,MAAA,CAAAjH,QAAA,GAAAiH,MAAA,CAAAjH,QAAA,CAAAyH,MAAA,WAAAD,IAAA;YAAA,OAAAF,iBAAA,CAAAI,QAAA,CAAAF,IAAA,CAAAvH,QAAA;UAAA;UAEA,IAAA0H,4BAAA,GAAAV,MAAA,CAAAjH,QAAA,CAAA4H,IAAA,WAAAJ,IAAA;YACA,OAAAA,IAAA,CAAAvH,QAAA;UACA,GAAA0H,4BAAA;UACA,IAAAA,4BAAA;YACA,IAAAE,SAAA,OAAAC,IAAA,CACAH,4BAAA,CAAAE,SAAA,CAAApJ,OAAA,gBACA;YACA;YACAoJ,SAAA,CAAAE,UAAA,CAAAF,SAAA,CAAAG,UAAA,KAAAL,4BAAA,CAAAM,OAAA;YACA,IAAAC,OAAA,GAAAL,SAAA,CACAM,WAAA,GACA1J,OAAA,WACAA,OAAA,UACA2J,KAAA;YACA;YACAnB,MAAA,CAAAjG,GAAA,GAAA2G,4BAAA,CAAA3G,GAAA;YACAiG,MAAA,CAAAoB,UAAA;cACAR,SAAA,EAAAF,4BAAA,CAAAE,SAAA;cACAK,OAAA,EAAAA,OAAA;cACAlH,GAAA,EAAAiG,MAAA,CAAAjG,GAAA;cACAsH,cAAA;YACA;UACA;QACA,QAEA;MACA,CACA;IAEA;IACA;IACAD,UAAA,WAAAA,WAAAE,IAAA;MAAA,IAAAC,MAAA;MAAA,IAAAX,SAAA,GAAAU,IAAA,CAAAV,SAAA;QAAAK,OAAA,GAAAK,IAAA,CAAAL,OAAA;QAAAlH,GAAA,GAAAuH,IAAA,CAAAvH,GAAA;QAAAsH,cAAA,GAAAC,IAAA,CAAAD,cAAA;MACA,SAAAlI,kBAAA;QACA;MACA;QACA,IAAAqI,GAAA,OAAAX,IAAA;QACA,IAAAY,KAAA,OAAAZ,IAAA,CAAAD,SAAA;QACA,IAAA1I,IAAA,OAAA2I,IAAA,CAAAI,OAAA;QACAjG,OAAA,CAAAC,GAAA,mBAAAoG,cAAA;QAEA,IAAAG,GAAA,IAAAC,KAAA,IAAAD,GAAA,IAAAtJ,IAAA,IAAAmJ,cAAA;UACA,KAAAK,SAAA;YACAH,MAAA,CAAAzH,SAAA,OAAA+G,IAAA,CAAAI,OAAA;UACA;UACA,KAAAlH,GAAA,GAAAA,GAAA;UACAiB,OAAA,CAAAC,GAAA,kBAAAlB,GAAA;UACA,KAAAxB,IAAA;UACA,KAAAwG,YAAA;QACA;UACA,KAAA5F,kBAAA;QACA;MACA;IACA;IACA;IACAsE,IAAA,WAAAA,KAAA;MACA;MACA,KAAApE,GAAA,MAAAsI,MAAA,CAAAvK,OAAA,CAAAC,GAAA,CAAAuK,qBAAA,yBAAAD,MAAA,MAAAtJ,QAAA,CAAAyH,KAAA,OAAA6B,MAAA,MAAA3F,MAAA,CAAAC,KAAA,CAAAF,SAAA;MACA,IAAA8F,KAAA,QAAAxI,GAAA;MACA,KAAAE,EAAA,OAAAuI,SAAA,CAAAD,KAAA;MACA,IAAAE,IAAA;MACA;MACA,IAAAC,aAAA;QACAC,OAAA;QAAA;QACAC,UAAA;QACAC,gBAAA;QACAV,KAAA,WAAAA,MAAA;UAAA,IAAAW,MAAA;UACA,KAAAF,UAAA,GAAAtH,UAAA;YACA;YACAmH,IAAA,CAAAxI,EAAA,CAAA8I,IAAA;YACAD,MAAA,CAAAD,gBAAA,GAAAvH,UAAA;cACAmH,IAAA,CAAAxI,EAAA,CAAA+I,KAAA;YACA,GAAAF,MAAA,CAAAH,OAAA;UACA,QAAAA,OAAA;QACA;QACAM,KAAA,WAAAA,MAAA;UACAC,YAAA,MAAAN,UAAA;UACAM,YAAA,MAAAL,gBAAA;UACA,KAAAV,KAAA;QACA;QACAgB,IAAA,WAAAA,KAAA;UACAD,YAAA,MAAAN,UAAA;UACAM,YAAA,MAAAL,gBAAA;QACA;MACA;MACA,KAAA5I,EAAA,CAAAmJ,MAAA,aAAAC,KAAA;QACAX,aAAA,CAAAP,KAAA;QACAM,IAAA,CAAAzI,YAAA,GAAAyI,IAAA,CAAAzI,YAAA;QACAyI,IAAA,CAAA/H,MAAA;QAEAgB,OAAA,CAAAC,GAAA,CAAA8G,IAAA,CAAAzI,YAAA;MACA;MACA,KAAAC,EAAA,CAAAqJ,SAAA,aAAAD,KAAA;QACA3H,OAAA,CAAAC,GAAA,CAAA0H,KAAA,CAAAxK,IAAA;QACA,IAAAwK,KAAA,CAAAxK,IAAA;UACA;UACA6J,aAAA,CAAAO,KAAA;QACA,WAAAI,KAAA,CAAAxK,IAAA;UACA6C,OAAA,CAAAC,GAAA,cAAA1D,SAAA;QACA,WAAAoL,KAAA,CAAAxK,IAAA;UACA4J,IAAA,CAAA/D,YAAA;QACA,WAAA2E,KAAA,CAAAxK,IAAA;UACA4J,IAAA,CAAA/D,YAAA;QACA,WAAA2E,KAAA,CAAAxK,IAAA,CAAAsI,QAAA;UACAsB,IAAA,CAAA/D,YAAA;QACA,WAAA2E,KAAA,CAAAxK,IAAA;UACA4J,IAAA,CAAArE,KAAA,CAAA1F,UAAA,CAAA6K,YAAA;QACA,WAAAF,KAAA,CAAAxK,IAAA;UACA;QAAA,CACA,UACAwK,KAAA,CAAAxK,IAAA,yBACAwK,KAAA,CAAAxK,IAAA,iBACA;UACA4J,IAAA,CAAA/D,YAAA;QACA,WAAA2E,KAAA,CAAAxK,IAAA;UACA4J,IAAA,CAAA/D,YAAA;QACA,WAAA2E,KAAA,CAAAxK,IAAA;UACA4J,IAAA,CAAApE,uBAAA;UACA;UACA;UACA;UACA;QACA;UACAoE,IAAA,CAAAe,QAAA;QACA;MACA;MACA,KAAAvJ,EAAA,CAAAwJ,OAAA,aAAAJ,KAAA;QACAZ,IAAA,CAAAzI,YAAA,GAAAyI,IAAA,CAAAzI,YAAA;QACAyI,IAAA,CAAA/H,MAAA;QACAgB,OAAA,CAAAC,GAAA,CAAA8G,IAAA,CAAAzI,YAAA;QACA;QACA0I,aAAA,CAAAS,IAAA;QACAV,IAAA,CAAAtE,IAAA;MACA;IACA;IACA;IACAuF,IAAA,WAAAA,KAAA;MACA,SAAAzJ,EAAA;QACA,KAAAA,EAAA,CAAA+I,KAAA;QACA,KAAA/I,EAAA;MACA;IACA;IACA;IACA8I,IAAA,WAAAA,KAAA;MACA,SAAA9I,EAAA;QACA,KAAAA,EAAA,CAAA8I,IAAA,MAAAxJ,OAAA;QACA,KAAAA,OAAA;QACA,KAAAoK,cAAA;MACA;QACAC,KAAA;MACA;IACA;IACA;IACAC,WAAA,WAAAA,YAAAtK,OAAA;MACA,SAAAU,EAAA;QACA,KAAAA,EAAA,CAAA8I,IAAA,CAAAxJ,OAAA;MACA;QACAqK,KAAA;MACA;IACA;IACA;IACAJ,QAAA,WAAAA,SAAA;MAAA,IAAAM,MAAA;MACA,IAAAC,iBAAA,OAAArH,MAAA,CAAAC,KAAA,CAAAF,SAAA,EAAAG,IAAA,WAAAC,QAAA;QACAiH,MAAA,CAAA5J,aAAA,GAAA2C,QAAA,CAAAhE,IAAA;MACA;IACA;IACA;IACA8K,cAAA,WAAAA,eAAA;MAAA,IAAAK,MAAA;MACA,KAAA5B,SAAA;QACA,IAAA6B,SAAA,GAAAD,MAAA,CAAA5F,KAAA,CAAA8F,iBAAA;QACAD,SAAA,CAAAE,SAAA,GAAAF,SAAA,CAAAG,YAAA;MACA;IACA;IACA;IACAC,SAAA,WAAAA,UAAAC,KAAA;MAAA,IAAAC,GAAA,GAAAD,KAAA,CAAAC,GAAA;QAAAC,MAAA,GAAAF,KAAA,CAAAE,MAAA;QAAAC,QAAA,GAAAH,KAAA,CAAAG,QAAA;QAAAC,WAAA,GAAAJ,KAAA,CAAAI,WAAA;MACAhJ,OAAA,CAAAC,GAAA,CAAA4I,GAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,WAAA;MACA,IAAAD,QAAA,UAAAC,WAAA;QACA;UACA;UACAC,UAAA;UACAC,KAAA;UACA;UACA;QACA;MACA;QACA;UACA;UACAD,UAAA;UACAC,KAAA;UACA;UACA;UACAC,MAAA;QACA;MACA;IACA;IACA;IACAC,SAAA,WAAAA,UAAAC,KAAA;MAAA,IAAAR,GAAA,GAAAQ,KAAA,CAAAR,GAAA;QAAAC,MAAA,GAAAO,KAAA,CAAAP,MAAA;QAAAC,QAAA,GAAAM,KAAA,CAAAN,QAAA;QAAAC,WAAA,GAAAK,KAAA,CAAAL,WAAA;MACA,IAAAD,QAAA;QACA;UACA;UACA;UACAG,KAAA;UACA;UACAD,UAAA;QACA;MACA;QACA;UACA;UACA;UACAC,KAAA;UACA;UACAD,UAAA;QACA;MACA;IACA;IACAK,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,MAAA;MACA,KAAA1K,UAAA,GAAA2K,WAAA;QACA,IAAAD,MAAA,CAAAnM,IAAA;UACAmM,MAAA,CAAA7G,KAAA,CAAAxF,GAAA,CAAAyF,uBAAA;UACA;QAEA;MACA;IACA;IACA8G,oBAAA,WAAAA,qBAAA;MACAvF,aAAA,MAAArF,UAAA;IACA;IACA6K,UAAA,WAAAA,WAAA;MACA,IAAAC,EAAA,OAAA9D,IAAA;MACA,IAAA+D,KAAA;MAEAJ,WAAA;QACAG,EAAA,CAAAE,UAAA,CAAAF,EAAA,CAAAG,UAAA;QACAF,KAAA,CAAAzK,WAAA,OAAAoF,uBAAA,EAAAoF,EAAA;MACA;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAzJ,IAAA;IACA,KAAAuH,QAAA;IACA;IACA,KAAAwB,qBAAA;IACA,KAAAI,UAAA;EACA;EACAO,OAAA,WAAAA,QAAA;IACA,KAAAhC,cAAA;EACA;EACAiC,aAAA,WAAAA,cAAA;IACA;IACA,KAAAT,oBAAA;EACA;AACA", "ignoreList": []}]}