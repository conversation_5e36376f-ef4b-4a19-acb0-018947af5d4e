{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\suppliersRoom.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\bidOpeningHall\\suppliersRoom.vue", "mtime": 1753956690842}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON><PERSON><PERSON><PERSON>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"}, {"version": 3, "sources": ["suppliersRoom.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4GA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "suppliersRoom.vue", "sourceRoot": "src/views/bidOpeningHall", "sourcesContent": ["<template>\r\n  <div>\r\n    <BidHeadone ref=\"head\" @updateStatus=\"handleStatus\"></BidHeadone>\r\n    <div class=\"bidOpeningHall\">\r\n      <el-card id=\"main\" class=\"box-card\">\r\n        <div style=\"height: 10px;\">\r\n        <div style=\"padding: 5px 0 0 20px; float: left;\">平台当前时间：<span >{{ currentTime }}</span></div>\r\n        <div style=\"padding: 5px 20px 0 0; float: right;\">\r\n          连接状态：<span :style=\"`color:${isLink ? 'green' : 'red'}`\">{{\r\n            isLink ? \"已连接\" : \"已断连，请刷新重连\"\r\n          }}</span>\r\n        </div></div>\r\n        <Sign ref=\"signIn\" v-if=\"shouldRenderSign\" :projectInfo=\"projectInfo\" :userInfo=\"userInfo\" @sendMessage=\"operateSend\"></Sign>\r\n        <ready ref=\"ready\" v-if=\"node == 'ready' && projectInfo\" :projectInfo=\"projectInfo\"></ready>\r\n        <publicity ref=\"publicity\" v-if=\"node == 'publicity'\"></publicity>\r\n        <decryption ref=\"decryption\" v-if=\"node == 'decryption' && projectInfo\" :projectInfo=\"projectInfo\" :userInfo=\"userInfo\" :deadline=\"decryptionDeadline\" @sendMessage=\"operateSend\"></decryption>\r\n        <bidAnnouncement ref=\"bidAnnouncement\" v-if=\"node == 'bidAnnouncement'\"></bidAnnouncement>\r\n        <end ref=\"end\" v-if=\"node == 'end'\" @sendData=\"handleData\"></end>\r\n\r\n      </el-card>\r\n      <el-card class=\"box-card\" style=\"width: 15%;\">\r\n        <div class=\"im\">\r\n          <div class=\"im-title\">{{ userInfo.ent?userInfo.ent.entName :  ''}}</div>\r\n          <div ref=\"messagesContainer\" class=\"im-content\" :style=\"{height: syncedHeight }\">\r\n            <div v-for=\"(itemc, indexc) in recordContent\" :key=\"indexc\">\r\n              <div class=\"sysMessage\" v-if=\"itemc.type == 0\">\r\n              </div>\r\n              <div v-else>\r\n                <div class=\"word\" v-if=\"itemc.sendId !== userInfo.entId\">\r\n                  <div class=\"info\">\r\n                    <div class=\"message_time\">\r\n                      {{anonymous? \"*******\":itemc.sendName }}\r\n                    </div>\r\n                    <div class=\"info-content\">{{ itemc.content }}</div>\r\n                    <div class=\"message_time\">\r\n                      {{ formatBidOpeningTimeTwo(itemc.sendTime) }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"word-my\" v-else>\r\n                  <div class=\"info\">\r\n                    <div class=\"info-content\">{{ itemc.content }}</div>\r\n                    <div class=\"Sender_time\">\r\n                      {{ formatBidOpeningTimeTwo(itemc.sendTime) }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"im-operation\">\r\n            <div style=\"margin-right:5px\">\r\n              <el-input v-model=\"message\" placeholder=\"输入内容\"></el-input>\r\n            </div>\r\n            <el-button style=\"height: 36px;background: #176ADB;color:#fff\" @click=\"send\">发送</el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n    <Foot></Foot>\r\n\r\n    <el-dialog :visible.sync=\"secondQuoteVisible\" width=\"60%\">\r\n      <!-- 显示倒计时 -->\r\n      <el-table :data=\"quoteList\" border style=\"width: 100%;\" :cell-style=\"cellStyle\" :header-cell-style=\"headStyle\">\r\n        <el-table-column label=\"二次报价\">\r\n          <el-table-column type=\"index\" label=\"序号\" width=\"100\">\r\n          </el-table-column>\r\n          <el-table-column label=\"历史报价\" prop=\"quoteAmount\" align=\"center\">\r\n          </el-table-column>\r\n          <el-table-column label=\"报价大写\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatAmount(scope.row.quoteAmount) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"报价时间\" prop=\"createTime\" align=\"center\">\r\n          </el-table-column>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div class=\"decryption-countdown\">\r\n        <el-statistic @finish=\"finishSecondQuote\" format=\"二次报价结束倒计时：HH小时mm分ss秒\" :value=\"countdown\" time-indices>\r\n        </el-statistic>\r\n      </div>\r\n\r\n      <el-row :gutter=\"20\">\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"90px\">\r\n          <el-col :span=\"6\" :offset=\"3\">\r\n            <el-form-item label=\"报价金额：\">\r\n              <el-input v-model=\"form.quoteAmount\" placeholder=\"请输入报价金额\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"7\">\r\n            <el-form-item label=\"大写金额：\">\r\n              <el-input :disabled=\"true\" :value=\"returnConvertToChineseCurrency(form.quoteAmount)\" placeholder=\"请输入报价金额大写\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n        <el-col :span=\"5\">\r\n          <div style=\"display:flex;justify-content: center;align-items: center;\">\r\n            <el-button class=\"quote-button\" :disabled=\"isCountdownEnded\"  @click=\"submitQuote\">确 定</el-button>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Sign from \"./supplierComponent/sign.vue\";\r\nimport Ready from \"./supplierComponent/ready.vue\";\r\nimport publicity from \"./supplierComponent/publicity.vue\";\r\nimport decryption from \"./supplierComponent/decryption.vue\";\r\nimport bidAnnouncement from \"./supplierComponent/bidAnnouncement.vue\";\r\nimport end from \"./supplierComponent/end.vue\";\r\n\r\nimport {\r\n  formatDateOption,\r\n  getTodayStartWithDate,\r\n  getTodayEndWithDate,\r\n} from \"@/utils/index\";\r\nimport { bidInfo, chatHistory } from \"@/api/onlineBidOpening/info\";\r\nimport { getUserProfile } from \"@/api/system/user\";\r\nimport { convertToChineseCurrency } from \"@/utils/amount\";\r\nimport { listInfo } from \"@/api/evaluation/info\";\r\nimport { listQuote, addQuote } from \"@/api/again/quote\";\r\nimport { expertInfoById } from \"@/api/expert/review\";\r\nimport { approvalProcess,inquiringBidList } from \"@/api/expert/review\";\r\n\r\nconst baseUrl = process.env.VUE_APP_BASE_API;\r\nconst socketUrl = baseUrl.replace(\"http\", \"ws\").replace(\"/prod-api\", \"\");\r\nexport default {\r\n  components: { Sign, Ready, publicity, decryption, bidAnnouncement, end },\r\n  data() {\r\n    return {\r\n      node: \"sign\",\r\n      userInfo: {},\r\n      projectInfo: null,\r\n\r\n      // 二次报价表单\r\n      form: {\r\n        quoteAmount: \"\",\r\n        projectEvaluationId: \"\",\r\n        quoteAmountStr: \"\",\r\n      },\r\n      // 二次报价金额\r\n      rules: {\r\n        quoteAmount: [\r\n          { required: true, message: \"请输入金额\", trigger: \"blur\" },\r\n        ],\r\n        quoteAmountStr: [\r\n          { required: true, message: \"请输入金额大写\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      progress: [\r\n        {\r\n          itemName: \"资格性评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"符合性评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"技术标评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"商务标评审\",\r\n          status: 0,\r\n        },\r\n        {\r\n          itemName: \"投标报价打分\",\r\n          status: 0,\r\n        },\r\n      ],\r\n      // 二次报价历史报价\r\n      quoteList: [],\r\n      // 显示二次报价弹框\r\n      secondQuoteVisible: false,\r\n\r\n      decryptionDeadline: \"\",\r\n\r\n      url: \"\",\r\n      message: \"\",\r\n      text_content: \"\",\r\n      ws: null,\r\n\r\n      recordContent: [],\r\n      evaluationInfo: {},\r\n      timer: null, // 存储定时器引用\r\n      isCountdownEnded: false,\r\n      // 专家信息\r\n      expertList: {},\r\n      intervalId: null,\r\n      countdown: \"\",\r\n      num: 0,\r\n      isLink: false,\r\n      anonymous: true,\r\n      syncedHeight: '450px', // 初始高度\r\n      currentTime:null\r\n\r\n    };\r\n  },\r\n  computed: {\r\n    shouldRenderSign() {\r\n      return (\r\n        this.node === \"signIn\" &&\r\n        this.projectInfo &&\r\n        this.userInfo &&\r\n        Object.keys(this.userInfo).length > 0\r\n      );\r\n    },\r\n  },\r\n  watch: {\r\n    node: {\r\n      handler() {\r\n        setTimeout(() => {\r\n          var element = document.getElementById('main');\r\n          console.log('element.clientHeight', element.offsetHeight);\r\n          this.syncedHeight = element.offsetHeight - 120 + 'px'\r\n        }, 10);\r\n\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  methods: {\r\n    finishSecondQuote(isOk){\r\n      // this.isCountdownEnded = isOk;\r\n    },\r\n    // 初始化\r\n    init() {\r\n      // 获取开标项目信息\r\n      const promise1 = bidInfo({\r\n        bidOpeningTime: getTodayStartWithDate(),\r\n        bidOpeningEndTime: getTodayEndWithDate(),\r\n        projectId: this.$route.query.projectId,\r\n      }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.projectInfo = response.data;\r\n        } else {\r\n          this.$modal.msgwarning(response.msg);\r\n        }\r\n      });\r\n      // 获取用户信息\r\n      const promise2 = getUserProfile().then((response) => {\r\n        this.userInfo = response.data;\r\n        localStorage.setItem(\"userInfo\", JSON.stringify(this.userInfo));\r\n      });\r\n      // 获取项目评审信息\r\n      const promise3 = listInfo({\r\n        projectId: this.$route.query.projectId,\r\n      }).then((response) => {\r\n        if (response.code === 200) {\r\n          if (response.rows && response.rows.length > 0) {\r\n            this.evaluationInfo = response.rows[response.rows.length - 1];\r\n          } else {\r\n            this.evaluationInfo = {};\r\n            // this.$message.error(\"未查询到项目评审信息\");\r\n          }\r\n        } else {\r\n          this.$message.error(response.msg);\r\n        }\r\n      });\r\n      // 获取专家信息\r\n      const promise4 = expertInfoById({\r\n        projectId: this.$route.query.projectId,\r\n      }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.expertList = response.data;\r\n          localStorage.setItem(\"expertList\", JSON.stringify(this.expertList));\r\n        } else {\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n\r\n      Promise.all([promise1, promise2, promise3, promise4]).then((result) => {\r\n        this.join();\r\n        if (this.node == \"end\") {\r\n          this.$refs.end.getExpertReviewProgress();\r\n        }\r\n      });\r\n    },\r\n    // 获取当前开标室流程\r\n    handleStatus(data) {\r\n      this.anonymous = this.$store.getters.supplierBidOpenStatus >= 2 ? false : true;\r\n      switch (data) {\r\n        case \"签到\":\r\n          this.node = \"signIn\";\r\n          break;\r\n        case \"开标准备\":\r\n          this.node = \"ready\";\r\n          break;\r\n        case \"投标人公示\":\r\n          this.node = \"publicity\";\r\n          break;\r\n        case \"标书解密\":\r\n          this.node = \"decryption\";\r\n          break;\r\n        case \"唱标\":\r\n          this.node = \"bidAnnouncement\";\r\n          break;\r\n        case \"开标结束\":\r\n          this.node = \"end\";\r\n          break;\r\n      }\r\n    },\r\n    // 节点更新通知\r\n    updateStatus() {\r\n      this.$refs.head.getBidStatus();\r\n    },\r\n\r\n    // 转换大写的报价金额\r\n    returnConvertToChineseCurrency(money) {\r\n      return convertToChineseCurrency(money);\r\n    },\r\n    // 提交报价\r\n    submitQuote() {\r\n      if (this.isCountdownEnded) {\r\n        this.$message.warning(\"倒计时结束，禁止报价\");\r\n        this.secondQuoteVisible = false;\r\n        return;\r\n      }\r\n      var previousPrice = 0;\r\n      \r\n      if(this.quoteList.length===0){\r\n        previousPrice = this.projectInfo.project.budgetAmount;\r\n      }else{\r\n        previousPrice = this.quoteList[this.quoteList.length-1].quoteAmount;\r\n      }\r\n      if(this.form.quoteAmount>previousPrice){\r\n        this.$message.error(\"本次报价不能超过上次报价 \"+ previousPrice + \" 元\");\r\n      }\r\n      //提交报价\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          if (this.evaluationInfo.projectEvaluationId == null) {\r\n            listInfo({\r\n              projectId: this.$route.query.projectId,\r\n            }).then((response) => {\r\n              if (response.code === 200) {\r\n                if (response.rows && response.rows.length > 0) {\r\n                  this.evaluationInfo = response.rows[response.rows.length - 1];\r\n                  this.form.projectEvaluationId = this.evaluationInfo.projectEvaluationId;\r\n                } else {\r\n                  this.$message.error(\"没有评审信息！\");\r\n                  return\r\n                }\r\n              } else {\r\n                this.$message.success(response.msg);\r\n              }\r\n            });\r\n          } else {\r\n            this.form.projectEvaluationId = this.evaluationInfo.projectEvaluationId;\r\n          }\r\n          addQuote(this.form)\r\n            .then((result) => {\r\n              if (result.code === 200) {\r\n                this.$message.success(result.msg);\r\n                this.getQuoteList();\r\n                this.secondQuoteVisible = false;\r\n              } else {\r\n                this.$message.error(result.msg);\r\n              }\r\n            })\r\n            .catch((err) => { });\r\n        } else {\r\n          console.log(\"error submit!!\");\r\n          return false;\r\n        }\r\n      });\r\n      // 关闭弹窗时，清除定时器\r\n      if (this.timer) {\r\n        clearInterval(this.timer);\r\n      }\r\n    },\r\n    // 转换大写的报价金额\r\n    formatAmount(amount) {\r\n      return convertToChineseCurrency(amount);\r\n    },\r\n\r\n    // 格式化开标时间显示\r\n    formatBidOpeningTime(time) {\r\n      return formatDateOption(time, \"date\");\r\n    },\r\n    // 格式化开标时间显示 时-分-秒\r\n    formatBidOpeningTimeTwo(time) {\r\n      return formatDateOption(time, \"time\");\r\n    },\r\n    // 获取历史报价\r\n    getQuoteList() {\r\n      const queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 999,\r\n        params: {\r\n          projectId: this.$route.query.projectId,\r\n          entId: this.userInfo.entId,\r\n        },\r\n      };\r\n      listQuote(queryParams)\r\n        .then((result) => {\r\n          //存入数据\r\n          this.quoteList = result.rows;\r\n          console.log(this.quoteList.length + 1 == this.num);\r\n          if (this.quoteList.length + 1 == this.num) {\r\n            this.secondQuoteVisible = true;\r\n            // this.isCountdownEnded = true;\r\n          } else {\r\n            // this.isCountdownEnded = false;\r\n            this.secondQuoteVisible = false;\r\n          }\r\n        })\r\n        .catch((err) => { });\r\n    },\r\n    getExpertReviewProgress() {\r\n      approvalProcess(this.$route.query.projectId, this.expertList[0].resultId).then(\r\n        (response) => {\r\n          if (response.code == 200 && response.data.scoringMethodUinfo.scoringMethodItems) {\r\n            \r\n            const existingItemNames = response.data.scoringMethodUinfo.scoringMethodItems.map(item => item.itemName);\r\n            this.progress = this.progress.filter(item => existingItemNames.includes(item.itemName));\r\n\r\n            const evalProjectEvaluationProcess = this.progress.find((item) => {\r\n              return item.itemName == \"投标报价打分\";\r\n            }).evalProjectEvaluationProcess;\r\n            if (evalProjectEvaluationProcess) {\r\n              let startTime = new Date(\r\n                evalProjectEvaluationProcess.startTime.replace(\" \", \"T\") + \"Z\"\r\n              );\r\n              // 将 Date 对象加上30秒\r\n              startTime.setMinutes(startTime.getMinutes() + evalProjectEvaluationProcess.minutes);\r\n              var endTime = startTime\r\n                .toISOString()\r\n                .replace(\"T\", \" \")\r\n                .replace(\"Z\", \"\")\r\n                .split(\".\")[0];\r\n              // 将更新后的时间转换回字符串格式\r\n              this.num = evalProjectEvaluationProcess.num;\r\n              this.handleData({\r\n                startTime: evalProjectEvaluationProcess.startTime,\r\n                endTime: endTime,\r\n                num: this.num,\r\n                isAbandonedBid: 0\r\n              });\r\n            }\r\n          } else {\r\n\r\n          }\r\n        }\r\n      );\r\n\r\n    },\r\n    // 判断是否打开二次报价弹窗\r\n    handleData({ startTime, endTime, num, isAbandonedBid }) {\r\n      if (this.secondQuoteVisible == true) {\r\n        return;\r\n      } else {\r\n        const now = new Date(); // 当前时间\r\n        const start = new Date(startTime); // 开始时间\r\n        const end = new Date(endTime); // 结束时间\r\n        console.log(\"isAbandonedBid\",isAbandonedBid);\r\n        \r\n        if (now >= start && now <= end && isAbandonedBid==0) {\r\n          this.$nextTick(() => {\r\n            this.countdown = new Date(endTime);\r\n          });\r\n          this.num = num;\r\n          console.log(\"this.num\", this.num);\r\n          this.form = {};\r\n          this.getQuoteList();\r\n        } else {\r\n          this.secondQuoteVisible = false;\r\n        }\r\n      }\r\n    },\r\n    // 连接websocket\r\n    join() {\r\n      // this.url = `${socketUrl}/websocket/message/${this.userInfo.entId}/${this.$route.query.projectId}/0`;\r\n      this.url = `${process.env.VUE_APP_WEBSOCKET_API}/websocket/message/${this.userInfo.entId}/${this.$route.query.projectId}/0`;\r\n      const wsurl = this.url;\r\n      this.ws = new WebSocket(wsurl);\r\n      const self = this;\r\n      // 心跳检测函数\r\n      const ws_heartCheck = {\r\n        timeout: 5000, // 5秒\r\n        timeoutObj: null,\r\n        serverTimeoutObj: null,\r\n        start: function () {\r\n          this.timeoutObj = setTimeout(() => {\r\n            // 这里发送一个心跳包\r\n            self.ws.send(\"ping\");\r\n            this.serverTimeoutObj = setTimeout(() => {\r\n              self.ws.close(); // 如果超过一定时间还没重置，视为断开连接\r\n            }, this.timeout);\r\n          }, this.timeout);\r\n        },\r\n        reset: function () {\r\n          clearTimeout(this.timeoutObj);\r\n          clearTimeout(this.serverTimeoutObj);\r\n          this.start();\r\n        },\r\n        stop: function () {\r\n          clearTimeout(this.timeoutObj);\r\n          clearTimeout(this.serverTimeoutObj);\r\n        }\r\n      };\r\n      this.ws.onopen = function (event) {\r\n        ws_heartCheck.start();\r\n        self.text_content = self.text_content + \"已经打开开标室连接!\" + \"\\n\";\r\n        self.isLink = true;\r\n\r\n        console.log(self.text_content);\r\n      };\r\n      this.ws.onmessage = function (event) {\r\n        console.log(event.data);\r\n        if(event.data == \"ping\"){\r\n          // 心跳响应\r\n          ws_heartCheck.reset();\r\n        }else if (event.data == \"连接成功\") {\r\n          console.log(\"socketUrl\", socketUrl);\r\n        } else if (event.data == \"signIn\") {\r\n          self.updateStatus();\r\n        } else if (event.data == \"bidPublicity\") {\r\n          self.updateStatus();\r\n        } else if (event.data.includes(\"decryption\")) {\r\n          self.updateStatus();\r\n        } else if (event.data == \"supDecrytion\") {\r\n          self.$refs.decryption.initdataList();\r\n        } else if (event.data == \"nextStep\") {\r\n          // 不做任何操作\r\n        } else if (\r\n          event.data == \"bidAnnouncement\" ||\r\n          event.data == \"flowLabel\"\r\n        ) {\r\n          self.updateStatus();\r\n        } else if (event.data == \"end\") {\r\n          self.updateStatus();\r\n        } else if (event.data == \"evalAgainQuote\") {\r\n          self.getExpertReviewProgress();\r\n          // //打开二次报价弹窗\r\n          // self.form = {};\r\n          // //加载报价记录\r\n          // self.getQuoteList();\r\n        } else {\r\n          self.initChat();\r\n        }\r\n      };\r\n      this.ws.onclose = function (event) {\r\n        self.text_content = self.text_content + \"已经关闭开标室连接!\" + \"\\n\";\r\n        self.isLink = false;\r\n        console.log(self.text_content);\r\n        //断开后自动重连\r\n        ws_heartCheck.stop();\r\n        self.join();\r\n      };\r\n    },\r\n    // 断开websocket连接\r\n    exit() {\r\n      if (this.ws) {\r\n        this.ws.close();\r\n        this.ws = null;\r\n      }\r\n    },\r\n    // 发送消息\r\n    send() {\r\n      if (this.ws) {\r\n        this.ws.send(this.message);\r\n        this.message = \"\";\r\n        this.scrollToBottom();\r\n      } else {\r\n        alert(\"未连接到开标室服务器\");\r\n      }\r\n    },\r\n    // 发送消息\r\n    operateSend(message) {\r\n      if (this.ws) {\r\n        this.ws.send(message);\r\n      } else {\r\n        alert(\"未连接到开标室服务器\");\r\n      }\r\n    },\r\n    // 初始化聊天记录\r\n    initChat() {\r\n      chatHistory(this.$route.query.projectId).then((response) => {\r\n        this.recordContent = response.data;\r\n      });\r\n    },\r\n    // 处理滚动\r\n    scrollToBottom() {\r\n      this.$nextTick(() => {\r\n        const container = this.$refs.messagesContainer;\r\n        container.scrollTop = container.scrollHeight;\r\n      });\r\n    },\r\n    // 表格头颜色\r\n    headStyle({ row, column, rowIndex, columnIndex }) {\r\n      console.log(row, column, rowIndex, columnIndex);\r\n      if (rowIndex === 0 && columnIndex === 0) {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          background: \"#1C57A7\",\r\n          color: \"#fff\",\r\n          \"font-size\": \"16px\",\r\n          \"font-weight\": \"700\",\r\n        };\r\n      } else {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          background: \"#176ADB\",\r\n          color: \"#fff\",\r\n          \"font-size\": \"16px\",\r\n          \"font-weight\": \"700\",\r\n          border: \"0px\",\r\n        };\r\n      }\r\n    },\r\n    // 表格样式\r\n    cellStyle({ row, column, rowIndex, columnIndex }) {\r\n      if (rowIndex % 2 === 0) {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          \"font-weight\": \"700\",\r\n          color: \"#000\",\r\n          \"font-size\": \"14px\",\r\n          background: \"#FFFFFF\",\r\n        };\r\n      } else {\r\n        return {\r\n          \"text-align\": \"center\",\r\n          \"font-weight\": \"700\",\r\n          color: \"#000\",\r\n          \"font-size\": \"14px\",\r\n          background: \"#F5F5F5\",\r\n        };\r\n      }\r\n    },\r\n    startProgressInterval() {\r\n      this.intervalId = setInterval(() => {\r\n        if (this.node == \"end\") {\r\n          this.$refs.end.getExpertReviewProgress();\r\n          //在这里增加 供应商消息刷新页面接口\r\n\r\n        }\r\n      }, 5000); // 每隔 5 秒调用一次\r\n    },\r\n    stopProgressInterval() {\r\n      clearInterval(this.intervalId);\r\n    },\r\n    updateTime() {\r\n      var ct = new Date();\r\n      var _this = this;\r\n\r\n      setInterval(function(){\r\n        ct.setSeconds(ct.getSeconds() + 1);\r\n        _this.currentTime = formatDateOption(ct, \"cdatetime\");\r\n      }, 1000); // 每秒更新时间\r\n    }\r\n  },\r\n  created() { },\r\n  mounted() {\r\n    this.init();\r\n    this.initChat();\r\n    // 在组件挂载后启动定时器\r\n    this.startProgressInterval();\r\n    this.updateTime();\r\n  },\r\n  updated() {\r\n    this.scrollToBottom();\r\n  },\r\n  beforeDestroy() {\r\n    // 在组件销毁前清除定时器\r\n    this.stopProgressInterval();\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .el-card__body {\r\n  padding: 0;\r\n}\r\n</style>\r\n\r\n<style scoped lang=\"scss\">\r\n.bidOpeningHall {\r\n  position: relative;\r\n  background-color: #f5f5f5;\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n  justify-content: center;\r\n  align-content: flex-start;\r\n  align-items: flex-start;\r\n}\r\n.box-card {\r\n  min-height: 600px;\r\n  width: 50%;\r\n  margin: 15px 5px;\r\n}\r\n.im {\r\n  .im-title {\r\n    width: 100%;\r\n    height: 50px;\r\n    background: #176adb;\r\n\r\n    font-weight: 500;\r\n    font-size: 16px;\r\n    color: #ffffff;\r\n    letter-spacing: 0;\r\n\r\n    line-height: 50px;\r\n    text-align: center;\r\n  }\r\n  .im-content {\r\n    margin: 10px;\r\n    background: #f5f5f5;\r\n    // height: 450px;\r\n    overflow-y: auto;\r\n  }\r\n  .im-operation {\r\n    display: flex;\r\n    margin: 0 10px;\r\n    margin-bottom: 10px;\r\n    overflow: auto;\r\n  }\r\n}\r\n.im-content {\r\n  .word {\r\n    display: flex;\r\n    margin-bottom: 30px;\r\n\r\n    img {\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 50%;\r\n    }\r\n    .info {\r\n      width: 47%;\r\n      margin-left: 10px;\r\n      .Sender_time {\r\n        padding-right: 12px;\r\n        padding-top: 5px;\r\n        font-size: 12px;\r\n        color: rgba(51, 51, 51, 0.8);\r\n        margin: 0;\r\n        height: 20px;\r\n      }\r\n      .message_time {\r\n        font-size: 12px;\r\n        color: rgba(51, 51, 51, 0.8);\r\n        margin: 0;\r\n        height: 20px;\r\n        line-height: 20px;\r\n        margin-top: -5px;\r\n        margin-top: 5px;\r\n      }\r\n      .info-content {\r\n        word-break: break-all;\r\n        // max-width: 45%;\r\n        display: inline-block;\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        background: #fff;\r\n        position: relative;\r\n        margin-top: 8px;\r\n        background: #dbdbdb;\r\n        border-radius: 4px;\r\n      }\r\n      //小三角形\r\n      .info-content::before {\r\n        position: absolute;\r\n        left: -8px;\r\n        top: 8px;\r\n        content: \"\";\r\n        border-right: 10px solid #dbdbdb;\r\n        border-top: 8px solid transparent;\r\n        border-bottom: 8px solid transparent;\r\n      }\r\n    }\r\n  }\r\n\r\n  .word-my {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    // margin-bottom: 30px;\r\n    img {\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 50%;\r\n    }\r\n    .info {\r\n      width: 90%;\r\n      // margin-left: 10px;\r\n      text-align: right;\r\n      // position: relative;\r\n      display: flex;\r\n      align-items: flex-end;\r\n      flex-wrap: wrap;\r\n      flex-direction: column;\r\n      .info-content {\r\n        word-break: break-all;\r\n        max-width: 45%;\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        // float: right;\r\n        margin-right: 10px;\r\n        position: relative;\r\n        margin-top: 8px;\r\n        background: #a3c3f6;\r\n        text-align: left;\r\n        border-radius: 4px;\r\n      }\r\n      .Sender_time {\r\n        padding-right: 12px;\r\n        padding-top: 5px;\r\n        font-size: 12px;\r\n        color: rgba(51, 51, 51, 0.8);\r\n        margin: 0;\r\n        height: 20px;\r\n      }\r\n      //小三角形\r\n      .info-content::after {\r\n        position: absolute;\r\n        right: -8px;\r\n        top: 8px;\r\n        content: \"\";\r\n        border-left: 10px solid #a3c3f6;\r\n        border-top: 8px solid transparent;\r\n        border-bottom: 8px solid transparent;\r\n      }\r\n    }\r\n  }\r\n}\r\n.countdown-timer {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n  color: #fff;\r\n  margin: 30px 0;\r\n\r\n  width: 30%;\r\n  height: 70px;\r\n\r\n  background: #176adb;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n.quote-button {\r\n  width: 100px;\r\n  background: #176adb;\r\n\r\n  color: #fff;\r\n  font-weight: 700;\r\n\r\n  border-radius: 5px;\r\n}\r\n.exper-title {\r\n  height: 45px;\r\n  background: #176adb;\r\n  display: flex;\r\n  justify-content: center;\r\n\r\n  font-family: SourceHanSansSC-Bold;\r\n  font-weight: 700;\r\n  font-size: 16px;\r\n  color: #ffffff;\r\n  letter-spacing: 0;\r\n  align-items: center;\r\n}\r\n.expert-title-second {\r\n  height: 40px;\r\n  background: #1c57a7;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n.decryption-countdown {\r\n  width: 50%;\r\n  height: 105px;\r\n  margin: 10px 25%;\r\n\r\n  // background: #176adb;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  ::v-deep .number {\r\n    color: #000;\r\n    font-weight: 700;\r\n  }\r\n}\r\n</style>\r\n"]}]}